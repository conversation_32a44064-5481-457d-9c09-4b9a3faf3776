import { computed, inject, Injectable, Signal } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep } from 'lodash';
import { DateTime } from 'luxon';

import { Day, filterByRequiredKeys, isNotNil, isSameDay, TimeInMilliseconds } from '@malou-io/package-utils';

import {
    BusinessHoursState,
    DEFAULT_HOURS_PERIOD,
    OtherHoursState,
    OtherServiceSchedules,
    ScheduleWithIsClosed,
    SpecialHoursState,
} from ':modules/informations/hours-modal/hours-modal.interface';
import { HoursType, MyDate, OtherPeriod, Period, SpecialDatePeriod, SpecialTimePeriod, TIME_24, TimePeriod } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

@Injectable({
    providedIn: 'root',
})
export class HoursModalService {
    private readonly _translateService = inject(TranslateService);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);

    /**
     * Business Hours methods
     */

    getRegularHoursFromBusinessHoursState(schedules: ScheduleWithIsClosed[]): TimePeriod[] {
        const regularHours: TimePeriod[] = [];
        const DAYS = Object.values(Day);

        schedules.forEach((schedule) => {
            if (schedule.isClosed) {
                regularHours.push(
                    ...schedule.selectedDays.map(
                        (day) => new TimePeriod({ openDay: day, closeDay: day, isClosed: true, openTime: null, closeTime: null })
                    )
                );
            } else {
                const mergedPeriods = this._mergeIntertwinedPeriods(schedule.periods);
                mergedPeriods.forEach((period) => {
                    regularHours.push(
                        ...schedule.selectedDays.map((day) => {
                            if (period.isFullDay()) {
                                period.cleanFullDay();
                            }
                            const dayIndex = DAYS.indexOf(day);
                            const closeDay = period.openTime! < period.closeTime! ? day : DAYS[(dayIndex + 1) % DAYS.length];
                            return new TimePeriod({
                                openDay: day,
                                closeDay,
                                isClosed: false,
                                openTime: period.openTime,
                                closeTime: period.closeTime,
                            });
                        })
                    );
                });
            }
        });

        return regularHours;
    }

    getAvailableDaysForBusinessHours(scheduleDays: Day[], schedules: ScheduleWithIsClosed[]): Day[] {
        const allSelectedDays = schedules.map((s) => s.selectedDays).flat();
        return Object.values(Day).filter((d) => !allSelectedDays.includes(d) || scheduleDays.includes(d));
    }

    mapRegularHoursToBusinessHoursState(regularHours: TimePeriod[]): BusinessHoursState {
        // Create schedules from regular hours grouped by opening days with the same periods
        const schedules: ScheduleWithIsClosed[] = [];

        const openDaysRegularHours = regularHours.filter((period) => !period.isClosed);
        const regularHoursByDay = openDaysRegularHours.reduce<Record<Day, TimePeriod[]>>(
            (acc, period) => {
                const day = period.openDay;
                acc[day] = [...(acc[day] ?? []), period];
                return acc;
            },
            {} as Record<Day, TimePeriod[]>
        );

        for (const day of Object.keys(regularHoursByDay)) {
            const periods: TimePeriod[] = regularHoursByDay[day];
            const existingSchedule = schedules.find((schedule) => {
                const existingPeriods = schedule.periods;
                return (
                    existingPeriods.length === periods.length &&
                    existingPeriods.every((existingPeriod) =>
                        periods.some(
                            (period) => period.openTime === existingPeriod.openTime && period.closeTime === existingPeriod.closeTime
                        )
                    )
                );
            });

            if (existingSchedule) {
                existingSchedule.selectedDays.push(day as Day);
            } else {
                const cleanPeriods = periods.map((period) => new Period(period));
                cleanPeriods.forEach((period) => {
                    if (period.openTime === '00:00' && period.closeTime === '24:00') {
                        period.openTime = TIME_24;
                        period.closeTime = null;
                    }
                });
                schedules.push({
                    selectedDays: [day as Day],
                    periods: cleanPeriods,
                    isClosed: false,
                    availableDays: Object.values(Day),
                });
            }
        }

        // Add closed days schedule if any
        const closedDays = Array.from(new Set(regularHours.filter((period) => period.isClosed).map((period) => period.openDay)));
        if (closedDays.length > 0) {
            const closedDaysSchedule = {
                selectedDays: closedDays,
                periods: [cloneDeep(DEFAULT_HOURS_PERIOD)],
                isClosed: true,
                availableDays: Object.values(Day),
            };
            schedules.push(closedDaysSchedule);
        }

        // Sort schedules by the first day of the week, and periods by open time
        schedules.sort((scheduleA, scheduleB) => {
            const minDayScheduleA = Math.min(...scheduleA.selectedDays.map((day) => Object.values(Day).indexOf(day)));
            const minDayScheduleB = Math.min(...scheduleB.selectedDays.map((day) => Object.values(Day).indexOf(day)));
            return minDayScheduleA - minDayScheduleB;
        });
        schedules.forEach((schedule) => {
            schedule.periods.sort((periodA, periodB) => (periodA.openTime ?? '').localeCompare(periodB.openTime ?? ''));
        });

        return { schedules, hasBeenTouched: false };
    }

    getBusinessHoursErrors(schedules: ScheduleWithIsClosed[]): string[] {
        const missingDaysError = this._getMissingDaysErrorMessage(schedules);
        const allInvalidPeriodsError = this._getAllInvalidPeriodsErrorMessage(schedules);
        const overlappingPeriodsError = this._getOverlappingPeriodsErrorMessage(schedules);
        const allClosedSchedulesError = this._getAllClosedSchedulesErrorMessage(schedules);

        return [missingDaysError, allInvalidPeriodsError, overlappingPeriodsError, allClosedSchedulesError].filter(isNotNil);
    }

    private _getMissingDaysErrorMessage(schedules: ScheduleWithIsClosed[]): string | null {
        const missingDays = this.getAvailableDaysForBusinessHours([], schedules);
        if (missingDays.length === 0) {
            return null;
        }
        const missingDaysString = missingDays.map((day) => this._enumTranslatePipe.transform(day, 'days')).join(', ');
        return this._translateService.instant('information.business_hours.missing_days', { days: missingDaysString });
    }

    private _getAllInvalidPeriodsErrorMessage(schedules: ScheduleWithIsClosed[]): string | null {
        const allPeriodsAreValid = schedules.every((schedule) =>
            schedule.periods.every((period) => period.openTime !== null && (period.openTime === TIME_24 || period.closeTime !== null))
        );
        if (allPeriodsAreValid) {
            return null;
        }
        return this._translateService.instant('information.hours.invalid_form_regular');
    }

    private _getOverlappingPeriodsErrorMessage(schedules: ScheduleWithIsClosed[]): string | null {
        const timePeriodsInMs = schedules.map((schedule) =>
            filterByRequiredKeys(schedule.periods, ['openTime', 'closeTime']).map((period) => {
                const startOfWeek = DateTime.now()
                    .startOf('week')
                    .plus({ days: Object.values(Day).indexOf(schedule.selectedDays[0]) })
                    .toJSDate();
                return {
                    openTime: this._createDateWithTime(startOfWeek, period.openTime ?? null).getTime(),
                    closeTime:
                        this._createDateWithTime(startOfWeek, period.closeTime ?? null).getTime() +
                        (period.closeTime < period.openTime ? TimeInMilliseconds.DAY : 0), // Handle overnight periods,
                };
            })
        );
        let firstOverlappingPeriodIndex = -1;
        timePeriodsInMs.some((periods, scheduleIndex) =>
            periods.some((period, index) =>
                periods.some((otherPeriod, otherIndex) => {
                    if (index === otherIndex) {
                        return false;
                    }
                    const hasOverlap =
                        (period.openTime < otherPeriod.closeTime && period.closeTime > otherPeriod.openTime) ||
                        (otherPeriod.openTime < period.closeTime && otherPeriod.closeTime > period.openTime);
                    if (hasOverlap) {
                        firstOverlappingPeriodIndex = scheduleIndex;
                        return true;
                    }
                    return false;
                })
            )
        );
        if (firstOverlappingPeriodIndex === -1) {
            return null;
        }
        const scheduleDays = schedules[firstOverlappingPeriodIndex].selectedDays
            .sort((a, b) => Object.values(Day).indexOf(a) - Object.values(Day).indexOf(b))
            .map((day) => this._enumTranslatePipe.transform(day, 'days'));
        return this._translateService.instant('information.hours.overlapping_periods_regular', { days: scheduleDays.join(', ') });
    }

    private _getAllClosedSchedulesErrorMessage(schedules: ScheduleWithIsClosed[]): string | null {
        const everyRegularHoursAreClosed = schedules.every((schedule) => schedule.isClosed);
        if (everyRegularHoursAreClosed) {
            return this._translateService.instant('information.hours.every_regular_hours_are_closed');
        }
        return null;
    }

    /**
     * Other Hours methods
     */

    getOtherHoursFromOtherHoursState(services: OtherServiceSchedules[]): OtherPeriod[] {
        return services.map((service) => {
            const periods = service.schedules
                .map((schedule) =>
                    schedule.selectedDays
                        .map((selectedDay) => {
                            const mergedPeriods = this._mergeIntertwinedPeriods(schedule.periods);
                            return mergedPeriods.map((period) => {
                                if (period.isFullDay()) {
                                    period.cleanFullDay();
                                }
                                const days = Object.values(Day);

                                return new TimePeriod({
                                    openDay: selectedDay,
                                    closeDay:
                                        period.openTime! < period.closeTime!
                                            ? selectedDay
                                            : days[(days.indexOf(selectedDay) + 1) % days.length],
                                    openTime: period.openTime,
                                    closeTime: period.closeTime,
                                    isClosed: false,
                                });
                            });
                        })
                        .flat()
                )
                .flat();

            return {
                hoursType: service.type,
                periods,
            };
        });
    }

    mapOtherHoursToOtherHoursState(otherHours: OtherPeriod[], availableHoursTypes: HoursType[]): OtherHoursState {
        const otherHoursWithOnlyOpenPeriods = otherHours
            .map((otherHour) => {
                const periods = otherHour.periods.filter((period) => !period.isClosed);
                return { ...otherHour, periods };
            })
            .filter((otherHour) => otherHour.periods.length > 0);

        const services = otherHoursWithOnlyOpenPeriods.map((otherHour) => {
            const { schedules } = this.mapRegularHoursToBusinessHoursState(otherHour.periods);
            const schedulesWithoutIsClosed = schedules.map((schedule) => {
                const { isClosed: _, ...rest } = schedule;
                return rest;
            });

            return {
                type: otherHour.hoursType,
                schedules: schedulesWithoutIsClosed,
            };
        });

        return {
            services,
            availableHoursTypes,
            hasBeenTouched: false,
        };
    }

    getAvailableDaysForOtherHoursService(
        serviceIndex: number,
        scheduleDays: Day[],
        services: Signal<OtherServiceSchedules[]>
    ): Signal<Day[]> {
        return computed(() => {
            const allSelectedDays = services()[serviceIndex].schedules.flatMap((s) => s.selectedDays);
            return Object.values(Day).filter((d) => !allSelectedDays.includes(d) || scheduleDays.includes(d));
        });
    }

    getOtherHoursErrors(services: OtherServiceSchedules[]): string[] {
        const errors: string[] = [];

        const allPeriodsAreValid = services.every((service) =>
            service.schedules.every((schedule) =>
                schedule.periods.every((period) => period.openTime !== null && (period.openTime === TIME_24 || period.closeTime !== null))
            )
        );
        if (!allPeriodsAreValid) {
            errors.push(this._translateService.instant('information.hours.invalid_form_other'));
        }

        return errors;
    }

    /**
     * Special Hours methods
     */

    getSpecialHoursFromSpecialHoursState(specialDatePeriods: SpecialDatePeriod[]): SpecialTimePeriod[] {
        const specialTimePeriods: SpecialTimePeriod[] = [];
        specialDatePeriods.forEach((specialPeriod) => {
            const periodDaysList = specialPeriod.getDaysList();
            if (specialPeriod.isClosed) {
                periodDaysList.forEach((date) => {
                    specialTimePeriods.push(
                        new SpecialTimePeriod({
                            startDate: date,
                            endDate: date,
                            isClosed: true,
                            name: specialPeriod.name,
                            openTime: null,
                            closeTime: null,
                            isFromCalendarEvent: specialPeriod.isFromCalendarEvent,
                        })
                    );
                });
                return;
            }
            const isOvernightPeriod =
                specialPeriod.periods.length === 1 &&
                specialPeriod.periods[0].openTime &&
                specialPeriod.periods[0].closeTime &&
                specialPeriod.periods[0].openTime > specialPeriod.periods[0].closeTime &&
                periodDaysList.length > 1;

            if (isOvernightPeriod) {
                const period = specialPeriod.periods[0];
                // Create one period for each consecutive day pair
                for (let i = 0; i < periodDaysList.length - 1; i++) {
                    specialTimePeriods.push(
                        new SpecialTimePeriod({
                            startDate: periodDaysList[i],
                            endDate: periodDaysList[i + 1],
                            isClosed: false,
                            openTime: period.openTime,
                            closeTime: period.closeTime,
                            name: specialPeriod.name,
                            isFromCalendarEvent: specialPeriod.isFromCalendarEvent,
                        })
                    );
                }
                return;
            }
            periodDaysList.forEach((date) => {
                this._mergeIntertwinedPeriods(specialPeriod.periods).forEach((period) => {
                    const endDate = !period.closeTime || !period.openTime || period.openTime < period.closeTime ? date : date.getNextDay();
                    specialTimePeriods.push(
                        new SpecialTimePeriod({
                            startDate: date,
                            endDate,
                            isClosed: specialPeriod.isClosed,
                            openTime: period.openTime,
                            closeTime: period.closeTime,
                            name: specialPeriod.name,
                            isFromCalendarEvent: specialPeriod.isFromCalendarEvent,
                        })
                    );
                });
            });
        });
        specialTimePeriods.forEach((specialPeriod) => {
            if (specialPeriod.isFullDay()) {
                specialPeriod.cleanFullDay();
            }
        });
        return specialTimePeriods;
    }

    mapSpecialHoursToSpecialHoursState(specialHours: SpecialTimePeriod[], prefilledStartDate?: Date): SpecialHoursState {
        const specialPeriods = this._getSpecialDatePeriodsFromSpecialTimePeriods(specialHours);
        return { specialPeriods, calendarEvents: [], prefilledStartDate, hasBeenTouched: false };
    }

    getSpecialHoursErrors(allSpecialDatePeriods: SpecialDatePeriod[], regularHoursSchedules: ScheduleWithIsClosed[]): string[] {
        const errors: string[] = [];
        const specialDatePeriods = allSpecialDatePeriods.filter((specialPeriod) => specialPeriod.isNotPastYet());

        const noRegularHours =
            regularHoursSchedules.length === 0 ||
            regularHoursSchedules.every((schedule) => schedule.isClosed) ||
            regularHoursSchedules.every((schedule) => schedule.periods.every((period) => period.shouldBeClosed()));
        if (specialDatePeriods.length > 0 && noRegularHours) {
            errors.push(this._translateService.instant('information.hours.no_regular_hours'));
        }

        const allDatesAreValid = specialDatePeriods.every((specialPeriod) => specialPeriod.areDatesValid());
        if (!allDatesAreValid) {
            errors.push(this._translateService.instant('information.hours.invalid_dates_form_special'));
        }

        const allPeriodsAreValid = specialDatePeriods.every((specialPeriod) => specialPeriod.arePeriodsValid());
        if (!allPeriodsAreValid) {
            errors.push(this._translateService.instant('information.hours.invalid_period_form_special'));
        }

        const noDatePeriodOverlap = specialDatePeriods.every((specialPeriod, i) => {
            const currentPeriodStart = specialPeriod.startDate?.getDate();
            const currentPeriodEnd = specialPeriod.endDate?.getDate() ?? currentPeriodStart;
            if (!currentPeriodStart || !currentPeriodEnd) {
                return true;
            }
            const narrowedSpecialDatePeriods = specialDatePeriods.filter((_, j) => j > i);
            return narrowedSpecialDatePeriods.every((otherSpecialPeriod) => {
                const otherPeriodStart = otherSpecialPeriod.startDate?.getDate();
                const otherPeriodEnd = otherSpecialPeriod.endDate?.getDate();
                if (!otherPeriodStart || !otherPeriodEnd || currentPeriodEnd < otherPeriodStart || currentPeriodStart > otherPeriodEnd) {
                    return true;
                }

                // Check if periods overlap within the same date range
                return specialPeriod.periods.every((currentPeriod) =>
                    otherSpecialPeriod.periods.every((otherPeriod) => {
                        const currentOpenTime = this._createDateWithTime(currentPeriodStart, currentPeriod.openTime ?? null);
                        const currentCloseTime = this._createDateWithTime(currentPeriodEnd, currentPeriod.closeTime ?? null);
                        const otherOpenTime = this._createDateWithTime(otherPeriodStart, otherPeriod.openTime ?? null);
                        const otherCloseTime = this._createDateWithTime(otherPeriodEnd, otherPeriod.closeTime ?? null);

                        return (
                            (currentCloseTime && currentCloseTime <= otherOpenTime) || (otherCloseTime && otherCloseTime <= currentOpenTime)
                        );
                    })
                );
            });
        });
        if (!noDatePeriodOverlap) {
            errors.push(this._translateService.instant('information.hours.invalid_overlap_form_special'));
        }

        return errors;
    }

    noSpecialHoursForThisDate(date: Date, specialDatePeriods: SpecialDatePeriod[]): boolean {
        return !specialDatePeriods.some((specialPeriod) => {
            const specialPeriodStart = specialPeriod.startDate?.getDate();
            return specialPeriodStart && isSameDay(specialPeriodStart, date);
        });
    }

    sortSpecialDatePeriods = (a: SpecialDatePeriod, b: SpecialDatePeriod): number =>
        (b.startDate?.getDate().getTime() ?? Number.MAX_SAFE_INTEGER) - (a.startDate?.getDate().getTime() ?? Number.MAX_SAFE_INTEGER);

    private _getSpecialDatePeriodsFromSpecialTimePeriods(specialTimePeriods: SpecialTimePeriod[]): SpecialDatePeriod[] {
        const specialTimePeriodsGroupedByName = specialTimePeriods.reduce(
            (acc, s) => {
                const name = s.name ?? '';
                if (!acc[name]) {
                    acc[name] = [];
                }
                acc[name].push(s);
                return acc;
            },
            {} as Record<string, SpecialTimePeriod[]>
        );

        const specialDatePeriods = Object.values(specialTimePeriodsGroupedByName).flatMap((s) => {
            // Merge consecutive special time periods
            const mergedSpecialTimePeriods = this._mergeConsecutiveSpecialTimePeriods(s);

            // Merge special time periods with same startDate and endDate into a special date period
            const mergedSpecialDatePeriods = this._mergeSpecialTimePeriodsWithSameDateIntoSpecialDatePeriods(mergedSpecialTimePeriods);

            return mergedSpecialDatePeriods;
        });

        // Init full day periods
        specialDatePeriods.forEach((specialDatePeriod) => {
            specialDatePeriod.periods.forEach((period) => {
                if (period.openTime === '00:00' && period.closeTime === '24:00') {
                    period.openTime = TIME_24;
                    period.closeTime = null;
                }
            });
        });

        // Sort special date periods by start date
        specialDatePeriods.sort(this.sortSpecialDatePeriods);

        // Sort each period by open time
        specialDatePeriods.forEach((specialDatePeriod) => {
            specialDatePeriod.periods.sort((a, b) => {
                if (a.openTime === TIME_24) {
                    return -1;
                }
                if (b.openTime === TIME_24) {
                    return 1;
                }
                return (a.openTime ?? '').localeCompare(b.openTime ?? '');
            });
        });

        return specialDatePeriods;
    }

    private _mergeConsecutiveSpecialTimePeriods(specialTimePeriods: SpecialTimePeriod[]): SpecialTimePeriod[] {
        const mergedSpecialTimePeriods: SpecialTimePeriod[] = [];
        if (specialTimePeriods.length === 0) {
            return mergedSpecialTimePeriods;
        }

        // Sort special time periods by start date
        specialTimePeriods.sort((a, b) => a.startDate.getDate().getTime() - b.startDate.getDate().getTime());

        specialTimePeriods.forEach((specialTimePeriod) => {
            // Match special time period with same isClosed status and same open/close times, that are consecutive.
            const existingSpecialTimePeriodIndex = mergedSpecialTimePeriods.findIndex((s) => {
                const endDate = s.endDate ?? s.startDate;
                if (!endDate || !specialTimePeriod.startDate) {
                    return false;
                }
                const nextDay = endDate.getNextDay();
                return (
                    nextDay.equals(specialTimePeriod.startDate) &&
                    s.isClosed === specialTimePeriod.isClosed &&
                    (s.isClosed || (s.openTime === specialTimePeriod.openTime && s.closeTime === specialTimePeriod.closeTime))
                );
            });

            if (existingSpecialTimePeriodIndex > -1) {
                const existingSpecialTimePeriod = mergedSpecialTimePeriods[existingSpecialTimePeriodIndex];
                mergedSpecialTimePeriods[existingSpecialTimePeriodIndex] = new SpecialTimePeriod({
                    ...existingSpecialTimePeriod,
                    endDate: new MyDate(specialTimePeriod.startDate), // startDate is on purpose here to handle the case when the closeTime is after midnight
                });
            } else {
                mergedSpecialTimePeriods.push(specialTimePeriod);
            }
        });

        return mergedSpecialTimePeriods;
    }

    private _mergeSpecialTimePeriodsWithSameDateIntoSpecialDatePeriods(specialTimePeriods: SpecialTimePeriod[]): SpecialDatePeriod[] {
        return specialTimePeriods.reduce((acc, currentSpecialTimePeriod) => {
            const specialDatePeriodWithSameDates = acc.find(
                (specialDatePeriod) =>
                    specialDatePeriod.hasSameDates(currentSpecialTimePeriod) ||
                    specialDatePeriod.periodsAreConsecutive(currentSpecialTimePeriod)
            );
            if (!specialDatePeriodWithSameDates) {
                acc.push(
                    new SpecialDatePeriod({
                        name: currentSpecialTimePeriod.name,
                        startDate: currentSpecialTimePeriod.startDate,
                        endDate: currentSpecialTimePeriod.endDate,
                        periods: [new Period(currentSpecialTimePeriod)],
                        isClosed: currentSpecialTimePeriod.isClosed,
                        isFromCalendarEvent: currentSpecialTimePeriod.isFromCalendarEvent,
                    })
                );
            } else {
                specialDatePeriodWithSameDates.periods.push(new Period(currentSpecialTimePeriod));
                specialDatePeriodWithSameDates.isClosed = specialDatePeriodWithSameDates.isClosed || currentSpecialTimePeriod.isClosed;
                specialDatePeriodWithSameDates.isFromCalendarEvent =
                    specialDatePeriodWithSameDates.isFromCalendarEvent || currentSpecialTimePeriod.isFromCalendarEvent;
            }

            return acc;
        }, [] as SpecialDatePeriod[]);
    }

    /**
     * Common methods
     */

    private _mergeIntertwinedPeriods(periods: Period[]): Period[] {
        // If a period is full day, then the merge result is a single full day period
        const fullDayPeriod = periods.find((period) => period.isFullDay());
        if (fullDayPeriod) {
            return [fullDayPeriod];
        }
        if (periods.length === 1) {
            return periods;
        }

        // Sort periods by open time, then close time
        const sortedPeriods = periods.sort(
            ({ openTime: openTimeA, closeTime: closeTimeA }, { openTime: openTimeB, closeTime: closeTimeB }) => {
                if (!openTimeA) {
                    return openTimeB ? -1 : 0;
                }
                if (!openTimeB) {
                    return 1;
                }
                if (openTimeA < openTimeB) {
                    return -1;
                }
                if (openTimeA > openTimeB) {
                    return 1;
                }
                if (!closeTimeA) {
                    return closeTimeB ? -1 : 0;
                }
                if (!closeTimeB) {
                    return 1;
                }
                return closeTimeA < closeTimeB ? -1 : closeTimeA > closeTimeB ? 1 : 0;
            }
        );

        // Merge periods that are intertwined
        const mergedPeriods = sortedPeriods.reduce<Period[]>((acc, currentPeriod) => {
            const lastMergedPeriod = acc[acc.length - 1];
            if (
                lastMergedPeriod &&
                lastMergedPeriod.closeTime &&
                currentPeriod.openTime &&
                lastMergedPeriod.closeTime >= currentPeriod.openTime
            ) {
                const maxCloseTime =
                    !currentPeriod.closeTime || lastMergedPeriod.closeTime > currentPeriod.closeTime
                        ? lastMergedPeriod.closeTime
                        : currentPeriod.closeTime;
                lastMergedPeriod.closeTime = maxCloseTime;
            } else {
                acc.push(currentPeriod);
            }
            return acc;
        }, []);

        return mergedPeriods;
    }

    private _createDateWithTime = (date: Date, time: string | null): Date => {
        const newDate = new Date(date);
        if (time) {
            const [hours, minutes] = time.split(':').map(Number);
            newDate.setHours(hours || 0, minutes || 0);
        }
        return newDate;
    };
}
