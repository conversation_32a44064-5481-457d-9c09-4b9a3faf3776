import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import {
    CheckForStoreLocatorRestaurantPagesResponseDto,
    GenerateStoreLocatorContentResponseDto,
    GenerateStoreLocatorStorePageContentBodyDto,
    GetStoreLocatorCentralizationDraftDto,
    GetStoreLocatorDraftPagesDto,
    GetStoreLocatorOrganizationJobResponseDto,
    HandleStartStoreLocatorPublicationResponseDto,
    StartStoreLocatorPagesGenerationParamsDto,
    StartStoreLocatorPagesGenerationResponseDto,
    StartStoreLocatorStorePublicationParamsDto,
    StoreLocatorOrganizationConfigurationResponseDto,
    SuccessResponse,
    UpdateOrganizationConfigurationAiSettingsBodyDto,
    UpdateOrganizationConfigurationLanguagesBodyDto,
    UpdateOrganizationConfigurationStorePagesBodyDto,
    UpdateStoreLocatorStorePagesBodyDto,
    WatchStoreLocatorJobResponseDto,
    WatchStoreLocatorPagesGenerationParamsDto,
    WatchStoreLocatorStorePublicationParamsDto,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { environment } from ':environments/environment';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';

export interface RoiInsightsCreationState {
    wasLastResultSeen: boolean;
    creationStartDate: Date | null;
    creationEstimatedTime: number;
}

@Injectable({
    providedIn: 'root',
})
export class StoreLocatorService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/store-locator`;

    private readonly _http = inject(HttpClient);

    getPages(organizationId: string): Observable<ApiResultV2<GetStoreLocatorDraftPagesDto>> {
        return this._http.get<ApiResultV2<GetStoreLocatorDraftPagesDto>>(`${this.API_BASE_URL}/${organizationId}/pages`);
    }

    getCentralizationPages(organizationId: string): Observable<ApiResultV2<GetStoreLocatorCentralizationDraftDto>> {
        return this._http.get<ApiResultV2<GetStoreLocatorCentralizationDraftDto>>(`${this.API_BASE_URL}/${organizationId}/centralization`);
    }

    getOrganizationConfiguration(organizationId: string): Observable<StoreLocatorOrganizationConfiguration> {
        return this._http
            .get<
                ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration`)
            .pipe(map((data) => StoreLocatorOrganizationConfiguration.fromDto(data.data)));
    }

    updateOrganizationConfigurationAiSettings(
        organizationId: string,
        params: UpdateOrganizationConfigurationAiSettingsBodyDto
    ): Observable<StoreLocatorOrganizationConfiguration> {
        return this._http
            .put<
                ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration/ai-settings`, params)
            .pipe(map((data) => StoreLocatorOrganizationConfiguration.fromDto(data.data)));
    }

    updateStoreLocatorStorePages(organizationId: string, pages: UpdateStoreLocatorStorePagesBodyDto): Observable<boolean> {
        return this._http
            .put<ApiResultV2<SuccessResponse>>(`${this.API_BASE_URL}/${organizationId}/all-pages`, pages)
            .pipe(map((result) => result.data.success));
    }

    generateStoreLocatorContent({
        organizationId,
        body,
    }: {
        organizationId: string;
        body: GenerateStoreLocatorStorePageContentBodyDto;
    }): Observable<GenerateStoreLocatorContentResponseDto> {
        return this._http
            .post<ApiResultV2<GenerateStoreLocatorContentResponseDto>>(`${this.API_BASE_URL}/${organizationId}/generate-page-content`, body)
            .pipe(map((result) => result.data));
    }

    updateOrganizationConfigurationStorePages(
        organizationId: string,
        storePagesStyle: UpdateOrganizationConfigurationStorePagesBodyDto
    ): Observable<boolean> {
        return this._http
            .put<
                ApiResultV2<SuccessResponse>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration/store-pages`, storePagesStyle)
            .pipe(map((result) => result.data.success));
    }

    updateOrganizationConfigLanguages(
        organizationId: string,
        languages: UpdateOrganizationConfigurationLanguagesBodyDto['languages']
    ): Observable<StoreLocatorOrganizationConfiguration> {
        return this._http
            .put<
                ApiResultV2<StoreLocatorOrganizationConfigurationResponseDto>
            >(`${this.API_BASE_URL}/${organizationId}/organization-configuration/languages`, { languages })
            .pipe(map((data) => StoreLocatorOrganizationConfiguration.fromDto(data.data)));
    }

    startStoreLocatorPagesGeneration(params: StartStoreLocatorPagesGenerationParamsDto): Observable<string> {
        const { organizationId } = params;
        return this._http
            .get<ApiResultV2<StartStoreLocatorPagesGenerationResponseDto>>(`${this.API_BASE_URL}/${organizationId}/generate/start`)
            .pipe(map((result) => result.data.jobId));
    }

    watchStoreLocatorPagesGeneration(params: WatchStoreLocatorPagesGenerationParamsDto): Observable<WatchStoreLocatorJobResponseDto> {
        const { organizationId, jobId } = params;
        return this._http
            .get<ApiResultV2<WatchStoreLocatorJobResponseDto>>(`${this.API_BASE_URL}/${organizationId}/generate/watch/${jobId}`)
            .pipe(map((result) => result.data));
    }

    startStoreLocatorPublication(params: StartStoreLocatorStorePublicationParamsDto): Observable<string> {
        const { organizationId } = params;
        return this._http
            .get<ApiResultV2<HandleStartStoreLocatorPublicationResponseDto>>(`${this.API_BASE_URL}/${organizationId}/publish/start`)
            .pipe(map((result) => result.data.jobId));
    }

    watchStoreLocatorPublication(params: WatchStoreLocatorStorePublicationParamsDto): Observable<WatchStoreLocatorJobResponseDto> {
        const { organizationId, jobId } = params;
        return this._http
            .get<ApiResultV2<WatchStoreLocatorJobResponseDto>>(`${this.API_BASE_URL}/${organizationId}/publish/watch/${jobId}`)
            .pipe(map((result) => result.data));
    }

    checkForStoreLocatorRestaurantPages(organizationId: string): Observable<CheckForStoreLocatorRestaurantPagesResponseDto> {
        return this._http
            .get<
                ApiResultV2<CheckForStoreLocatorRestaurantPagesResponseDto>
            >(`${this.API_BASE_URL}/${organizationId}/check-for-restaurant-pages`)
            .pipe(map((result) => result.data));
    }

    getStoreLocatorOrganizationJobs(organizationId: string): Observable<GetStoreLocatorOrganizationJobResponseDto[]> {
        return this._http
            .get<ApiResultV2<GetStoreLocatorOrganizationJobResponseDto[]>>(`${this.API_BASE_URL}/${organizationId}/organization-jobs`)
            .pipe(map((result) => result.data));
    }

    sendSubscriptionRequestNotification(organizationId: string): Observable<SuccessResponse> {
        return this._http
            .get<ApiResultV2<SuccessResponse>>(`${this.API_BASE_URL}/${organizationId}/send-subscription-request`)
            .pipe(map((result) => result.data));
    }
}
