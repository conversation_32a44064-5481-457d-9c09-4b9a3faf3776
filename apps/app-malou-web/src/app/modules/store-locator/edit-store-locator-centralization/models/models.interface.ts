import { StoreLocatorCentralizationPageUpdatesDto } from '@malou-io/package-dto';

import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';

interface StoreLocatorCentralizationBlockDataUpdateWithStatus<T> {
    isModified: boolean;
    data: T | undefined;
}

export interface StoreLocatorCentralizationBlockTypeUpdateMap {
    [StoreLocatorCentralizationBlockType.MAP]: NonNullable<
        StoreLocatorCentralizationBlockDataUpdateWithStatus<StoreLocatorCentralizationPageUpdatesDto['mapComponents']>
    >;
}
