import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, OnInit, signal, WritableSignal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TranslateModule } from '@ngx-translate/core';

import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { EditStoreLocatorPageContext } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.context';
import { EditStoreLocatorPageModalInputData } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { getFontFamily } from ':modules/store-locator/shared/edit-store-locator/utils/inject-font-family';
import { ButtonComponent } from ':shared/components/button/button.component';
import { MenuButtonSize } from ':shared/components/menu-button-v3/menu-button-v3.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-store-locator-edit-centralization',
    templateUrl: './edit-store-locator-centralization.component.html',
    styleUrls: ['./edit-store-locator-centralization.component.scss'],
    imports: [
        MatButtonModule,
        MatProgressSpinnerModule,
        NgTemplateOutlet,
        MatIconModule,
        NgStyle,
        TranslateModule,
        NgClass,
        ButtonComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditStoreLocatorCentralizationModalComponent implements OnInit {
    private readonly _editStoreLocatorPageContext = inject(EditStoreLocatorPageContext);
    private readonly _dialogRef = inject(MatDialogRef<EditStoreLocatorCentralizationModalComponent>);

    readonly data: EditStoreLocatorPageModalInputData = inject(MAT_DIALOG_DATA);

    readonly MenuButtonSize = MenuButtonSize;
    readonly SvgIcon = SvgIcon;
    readonly StoreLocatorCentralizationBlockType = StoreLocatorCentralizationBlockType;

    readonly isLoading: WritableSignal<boolean> = signal(true);

    readonly textFontFamilyClass = computed(() => getFontFamily('primary'));

    readonly isBlockInError = computed(() => this._editStoreLocatorPageContext.isBlockInError().isError);

    readonly shouldDisableModal = computed(() => this._editStoreLocatorPageContext.shouldDisableModal());

    readonly shouldAllowToSaveAsDraftOrPublish = computed(() => false);

    readonly selectedBlock: WritableSignal<StoreLocatorCentralizationBlockType> = signal(StoreLocatorCentralizationBlockType.MAP);

    ngOnInit(): void {}

    closeEditModal(): void {
        this._dialogRef.close();
    }

    onSaveAsDraft(): void {
        console.log('Save as draft clicked');
    }

    onPublish(): void {
        console.log('Publish clicked');
    }
}
