import { computed, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { map, Observable } from 'rxjs';

import { StoreLocatorCentralizationBlockType } from ':modules/store-locator/edit-store-locator-centralization/edit-store-locator-centralization.interface';
import { StoreLocatorStoreCentralizationState } from ':modules/store-locator/edit-store-locator-centralization/models/store-locator-store-centralization-state';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/models/store-locator-organization-styles-configuration';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';

@Injectable({
    providedIn: 'root',
})
export class EditStoreLocatorCentralizationContext {
    private readonly _storeLocatorService = inject(StoreLocatorService);

    readonly isSavingChanges: WritableSignal<boolean> = signal(false);
    readonly isPublishing: WritableSignal<boolean> = signal(false);

    readonly dialogRef: WritableSignal<any> = signal(null);

    readonly organizationData: WritableSignal<{ id: string; name: string }> = signal({ id: '', name: '' });
    readonly organizationStyleConfiguration: WritableSignal<StoreLocatorOrganizationStylesConfiguration | null> = signal(null);

    readonly isBlockInError: WritableSignal<{
        blockType: StoreLocatorCentralizationBlockType;
        isError: boolean;
    }> = signal({
        blockType: StoreLocatorCentralizationBlockType.MAP,
        isError: false,
    });

    readonly shouldDisableModal = computed(() => this.isSavingChanges() || this.isPublishing());

    getCentralizationPages(organizationId: string): Observable<StoreLocatorStoreCentralizationState[] | null> {
        return this._storeLocatorService
            .getCentralizationPages(organizationId)
            .pipe(map((res) => res.data.centralizationPages.map((page) => new StoreLocatorStoreCentralizationState(page))));
    }
}
