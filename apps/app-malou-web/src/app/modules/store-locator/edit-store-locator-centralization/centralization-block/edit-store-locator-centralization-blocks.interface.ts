import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

export enum PropertyType {
    BackgroundColor = 'backgroundColor',
    Color = 'color',
    Fill = 'fill',
    BorderColor = 'borderColor',
    BorderRadius = 'borderRadius',
}

export interface StylesConfigurationUpdate {
    elementId: StoreLocatorRestaurantPageElementIds;
    data: {
        value: string;
        propertyType: PropertyType;
    }[];
}

export enum StoreLocatorInputType {
    TITLE = 'title',
    SUBTITLE = 'subtitle',
    CTA = 'cta',
    PHOTO = 'photo',
    TEXT = 'text',
}
