import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/models/store-locator-organization-config';

const STORE_LOCATOR_FONT_FAMILY_PREFIX = 'store-locator-font-family-';

export const getFontFamily = (familyClass: string): string => `${STORE_LOCATOR_FONT_FAMILY_PREFIX}${familyClass}`;

export function loadDynamicFont(fonts: StoreLocatorOrganizationConfiguration['styles']['fonts']) {
    const style = document.createElement('style');
    style.id = 'store-locator-fonts';
    const fontFaces = fonts.map((font) => createFontFace(font.class, font.src)).join('\n');
    style.innerHTML = fontFaces;
    document.head.appendChild(style);
}

function createFontFace(fontFamily: string, src: string) {
    return `
        @font-face {
            font-family: '${STORE_LOCATOR_FONT_FAMILY_PREFIX}${fontFamily}';
            src: url('${src}');
            font-display: swap;
        }
    `;
}
