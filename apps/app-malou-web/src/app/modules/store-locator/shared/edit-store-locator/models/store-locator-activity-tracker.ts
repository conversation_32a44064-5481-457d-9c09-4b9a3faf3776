import { HeapEventName, StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { StoreLocatorInputType } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';

export class StoreLocatorActivityTracker {
    private readonly _editContentActivityMap = new Map<string, Record<StoreLocatorPageBlockType, Record<StoreLocatorInputType, boolean>>>(
        []
    );
    private readonly _editStyleActivityMap = new Map<StoreLocatorRestaurantPageElementIds, boolean>([]);

    constructor(private readonly _heapService: HeapService) {
        this._editStyleActivityMap = this._getEmptyStyleActivityMap();
    }

    setRestaurantTrackingMap(restaurantId: string): void {
        if (!this._editContentActivityMap.has(restaurantId)) {
            this._editContentActivityMap.set(restaurantId, this._getEmptyActivityMap());
        }
    }

    trackEditContentActivity({
        restaurantId,
        block,
        element,
    }: {
        restaurantId: string;
        block: StoreLocatorPageBlockType;
        element: StoreLocatorInputType;
    }): void {
        if (!this._editContentActivityMap.has(restaurantId)) {
            this._editContentActivityMap.set(restaurantId, this._getEmptyActivityMap());
        }

        const activityMap = this._editContentActivityMap.get(restaurantId);

        if (!activityMap) {
            return;
        }
        const isElementAlreadyTracked = activityMap[block][element];
        if (isElementAlreadyTracked) {
            return;
        }
        activityMap[block][element] = true;
        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_EDIT_CONTENT, {
            block,
            element,
            restaurantId,
        });
    }

    trackEditStyleActivity({
        block,
        elementId,
    }: {
        block: StoreLocatorPageBlockType;
        elementId: StoreLocatorRestaurantPageElementIds;
    }): void {
        const isElementAlreadyTracked = this._editStyleActivityMap.get(elementId);
        if (isElementAlreadyTracked) {
            return;
        }

        this._editStyleActivityMap.set(elementId, true);

        this._heapService.track(HeapEventName.TRACKING_STORE_LOCATOR_EDIT_STYLE, {
            block,
            elementId,
        });
    }

    isElementContentEditTrackedForRestaurant({
        restaurantId,
        block,
        element,
    }: {
        restaurantId: string;
        block: StoreLocatorPageBlockType;
        element: StoreLocatorInputType;
    }): boolean {
        const activityMap = this._editContentActivityMap.get(restaurantId);
        return activityMap ? !!activityMap[block][element] : false;
    }

    isElementStyleEditTracked(elementId: StoreLocatorRestaurantPageElementIds): boolean {
        return this._editStyleActivityMap.get(elementId) || false;
    }

    private _getEmptyActivityMap(): Record<StoreLocatorPageBlockType, Record<StoreLocatorInputType, boolean>> {
        return Object.values(StoreLocatorPageBlockType).reduce(
            (acc, blockType) => {
                acc[blockType] = Object.values(StoreLocatorInputType).reduce(
                    (inputAcc, inputType) => {
                        inputAcc[inputType] = false;
                        return inputAcc;
                    },
                    {} as Record<StoreLocatorInputType, boolean>
                );
                return acc;
            },
            {} as Record<StoreLocatorPageBlockType, Record<StoreLocatorInputType, boolean>>
        );
    }

    private _getEmptyStyleActivityMap(): Map<StoreLocatorRestaurantPageElementIds, boolean> {
        return new Map<StoreLocatorRestaurantPageElementIds, boolean>(
            Object.values(StoreLocatorRestaurantPageElementIds).map((elementId) => [elementId, false])
        );
    }
}
