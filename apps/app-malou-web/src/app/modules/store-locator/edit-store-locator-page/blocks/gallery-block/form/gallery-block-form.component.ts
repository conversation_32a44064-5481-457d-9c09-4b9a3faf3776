import { ChangeDetectionStrategy, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import {
    PropertyType,
    StoreLocatorInputType,
} from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import {
    GalleryBlockContent,
    GalleryBlockContentForm,
    GalleryBlockContentFormInputValidation,
    GalleryBlockStyleData,
    GalleryBlockStyleForm,
} from ':modules/store-locator/edit-store-locator-page/blocks/gallery-block/gallery-block.interface';
import { EditStoreLocatorPageAiSuggestionComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/ai-suggestion/ai-suggestion.component';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { mapUpdateStylesConfiguration } from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { ImageUploaderComponent } from ':shared/components/image-uploader/image-uploader.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { TextAreaComponent } from ':shared/components/text-area/text-area.component';
import { Media } from ':shared/models';

@Component({
    selector: 'app-store-locator-edit-page-gallery-block-form',
    templateUrl: './gallery-block-form.component.html',
    styleUrls: ['./gallery-block-form.component.scss'],
    imports: [
        TranslateModule,
        MatTabsModule,
        MatIconModule,
        MatTooltipModule,
        MatButtonModule,
        StoreLocatorPageBlockFormComponent,
        InputTextComponent,
        TextAreaComponent,
        ReactiveFormsModule,
        ImageUploaderComponent,
        EditStoreLocatorPageColorSelectorComponent,
        EditStoreLocatorPageAiSuggestionComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageGalleryBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);

    readonly GalleryBlockContentFormInputValidation = GalleryBlockContentFormInputValidation;

    contentForm: FormGroup<GalleryBlockContentForm>;
    styleForm: FormGroup<GalleryBlockStyleForm>;

    readonly galleryBlockData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.GALLERY)?.()?.data
    );

    readonly title = computed(() => this.galleryBlockData()?.title ?? '');
    readonly subtitle = computed(() => this.galleryBlockData()?.subtitle ?? '');

    readonly uploadedMedias: WritableSignal<Media[]> = signal<Media[]>([]);

    constructor() {
        super();
        this._initContentForm();
        this._initStyleForm();
        toObservable(this.currentEditingRestaurant)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
    }

    get titleControl(): AbstractControl | null {
        return this.contentForm.get('title');
    }

    get subtitleControl(): AbstractControl | null {
        return this.contentForm.get('subtitle');
    }

    getMedia(index: number): Media | null {
        return this.uploadedMedias()?.[index] ?? null;
    }

    setMedia(index: number, media: Media | null): void {
        if (media) {
            this.uploadedMedias.update((medias) => {
                const updatedMedias = [...medias];
                updatedMedias[index] = media;
                return updatedMedias;
            });
            this._updateMedias();
        }
    }

    private _checkIfBlockInError(): void {
        const titleControl = this.titleControl;
        const subtitleControl = this.contentForm.get('subtitle');
        if (!titleControl || !subtitleControl) {
            return;
        }
        const isTitleInError = !!titleControl?.errors && titleControl.dirty;
        const isSubtitleInError = !!subtitleControl?.errors && subtitleControl.dirty;
        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.GALLERY,
            isError: isTitleInError || isSubtitleInError,
        });
    }

    private _initContentForm(): void {
        this._initMedias();
        this.contentForm = this._formBuilder.group({
            title: new FormControl(this.galleryBlockData()?.title ?? '', {
                validators: [Validators.required, Validators.maxLength(GalleryBlockContentFormInputValidation.TITLE_MAX_LENGTH)],
                nonNullable: true,
            }),
            subtitle: new FormControl(this.galleryBlockData()?.subtitle ?? '', {
                validators: [Validators.required, Validators.maxLength(GalleryBlockContentFormInputValidation.SUBTITLE_MAX_LENGTH)],
                nonNullable: true,
            }),
        });

        this.contentForm.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((value: GalleryBlockContent) => {
            const galleryBlockData = this.galleryBlockData();
            this._checkIfBlockInError();
            if (galleryBlockData) {
                this._trackEditContentChanges(value);
                this.storeLocatorPageState()?.updateBlock({
                    blockType: StoreLocatorPageBlockType.GALLERY,
                    blockData: {
                        ...galleryBlockData,
                        title: value.title,
                        subtitle: value.subtitle,
                    },
                });
            }
        });
    }

    private _patchContentForm(): void {
        const galleryBlockData = this.galleryBlockData();
        if (galleryBlockData) {
            this.contentForm.patchValue({
                title: galleryBlockData.title,
                subtitle: galleryBlockData.subtitle,
            });
            this._initMedias();
            this.contentForm.markAsPristine();
        }
    }

    private _initMedias(): void {
        const galleryBlockData = this.galleryBlockData();
        if (galleryBlockData) {
            this.uploadedMedias.set(
                galleryBlockData.images.map(
                    (image) =>
                        new Media({
                            urls: { original: image.url },
                            dimensions: {},
                        })
                )
            );
        }
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER,
            StoreLocatorRestaurantPageElementIds.GALLERY_TITLE,
        ]);

        const galleryBlockWrapperStyle = styleMap[StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER] || {};
        const galleryBlockTitleStyle = styleMap[StoreLocatorRestaurantPageElementIds.GALLERY_TITLE] || {};

        this.styleForm = this._formBuilder.group({
            general: this._formBuilder.group({
                titleColor: this._formBuilder.control(galleryBlockTitleStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(galleryBlockWrapperStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                textColor: this._formBuilder.control(galleryBlockWrapperStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });
        this.styleForm.valueChanges.subscribe((value) => {
            this._updateStyleConfiguration(value as GalleryBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: GalleryBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.GALLERY_WRAPPER,
                    data: [
                        {
                            value: value.general.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.general.textColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.GALLERY_TITLE,
                    data: [
                        {
                            value: value.general.titleColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
            ]);
            this.trackEditStyleActivity({
                block: StoreLocatorPageBlockType.GALLERY,
                changes: dataToUpdate,
            });
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }

    private _updateMedias(): void {
        const galleryBlockData = this.galleryBlockData();
        const uploadedMedias = this.uploadedMedias();
        if (galleryBlockData && uploadedMedias) {
            this.trackEditContentActivity({
                block: StoreLocatorPageBlockType.GALLERY,
                element: StoreLocatorInputType.PHOTO,
            });
            this.storeLocatorPageState()?.updateBlock({
                blockType: StoreLocatorPageBlockType.GALLERY,
                blockData: {
                    ...galleryBlockData,
                    images: galleryBlockData.images.map((image, index) => ({
                        ...image,
                        url: uploadedMedias[index]?.urls.original || image.url,
                    })),
                },
            });
        }
    }

    private _trackEditContentChanges(changes: GalleryBlockContent): void {
        const galleryBlockData = this.galleryBlockData();
        if (galleryBlockData) {
            if (galleryBlockData.title !== changes.title) {
                this.trackEditContentActivity({
                    block: StoreLocatorPageBlockType.GALLERY,
                    element: StoreLocatorInputType.TITLE,
                });
            }
            if (galleryBlockData.subtitle !== changes.subtitle) {
                this.trackEditContentActivity({
                    block: StoreLocatorPageBlockType.GALLERY,
                    element: StoreLocatorInputType.SUBTITLE,
                });
            }
            this._checkIfBlockInError();
        }
    }
}
