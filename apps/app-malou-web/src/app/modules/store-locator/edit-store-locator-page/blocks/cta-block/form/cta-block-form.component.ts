import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { skip } from 'rxjs';

import { StoreLocatorRestaurantPageElementIds } from '@malou-io/package-utils';

import {
    CtaBlockContentForm,
    CtaBlockContentFormData,
    CtaBlockContentFormInputValidation,
    CtaBlockStyleData,
    CtaBlockStyleForm,
    CtaButtonsFormGroup,
} from ':modules/store-locator/edit-store-locator-page/blocks/cta-block/cta-block.interface';
import { StoreLocatorPageBlockFormComponent } from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-block-form.component';
import {
    PropertyType,
    StoreLocatorInputType,
} from ':modules/store-locator/edit-store-locator-page/blocks/edit-store-locator-page-blocks.interface';
import { EditStoreLocatorPageAiSuggestionComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/ai-suggestion/ai-suggestion.component';
import { EditStoreLocatorPageCallToActionComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/call-to-action/call-to-action.component';
import { EditStoreLocatorPageColorSelectorComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/color-selector/color-selector.component';
import { EditStoreLocatorPageRadiusSliderComponent } from ':modules/store-locator/edit-store-locator-page/blocks/shared/radius-slider/radius-slider.component';
import { StoreLocatorPageBlockType } from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import {
    getRadiusValue,
    mapUpdateStylesConfiguration,
} from ':modules/store-locator/shared/edit-store-locator/utils/edit-store-locator-page.utils';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { UrlValidator } from ':shared/validators/url.validator';

@Component({
    selector: 'app-store-locator-edit-page-cta-block-form',
    templateUrl: './cta-block-form.component.html',
    styleUrls: ['./cta-block-form.component.scss'],
    imports: [
        TranslateModule,
        MatTabsModule,
        MatIconModule,
        MatTooltipModule,
        MatButtonModule,
        StoreLocatorPageBlockFormComponent,
        InputTextComponent,
        ReactiveFormsModule,
        MatExpansionModule,
        EditStoreLocatorPageColorSelectorComponent,
        EditStoreLocatorPageAiSuggestionComponent,
        EditStoreLocatorPageCallToActionComponent,
        EditStoreLocatorPageRadiusSliderComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoreLocatorPageCtaBlockFormComponent extends StoreLocatorPageBlockFormComponent {
    private readonly _formBuilder = inject(FormBuilder);
    private readonly _cdr = inject(ChangeDetectorRef);

    readonly CtaBlockContentFormInputValidation = CtaBlockContentFormInputValidation;

    contentForm: FormGroup<CtaBlockContentForm>;
    styleForm: FormGroup<CtaBlockStyleForm>;

    readonly isButtonsPanelExpanded: WritableSignal<boolean> = signal<boolean>(false);

    readonly ctaBlockData = computed(
        () => this.storeLocatorPageState()?.getBlockUpdatedData$(StoreLocatorPageBlockType.CALL_TO_ACTION)?.()?.data
    );

    readonly title = computed(() => this.ctaBlockData()?.title ?? '');

    readonly callToActionsSuggestions = computed(() => this.storeLocatorPageState()?.getCallToActionsSuggestions() ?? []);

    constructor() {
        super();
        toObservable(this.currentEditingRestaurant)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this._patchContentForm();
            });
        this._initContentForm();
        this._initStyleForm();
    }

    get titleControl(): AbstractControl | null {
        return this.contentForm.get('title');
    }

    get ctaButtons(): FormArray<CtaButtonsFormGroup> {
        return this.contentForm.get('ctaButtons') as FormArray<CtaButtonsFormGroup>;
    }

    getCtaTextControl(index: number): FormControl | null {
        return this.ctaButtons.at(index).get('text') as FormControl | null;
    }

    getCtaUrlControl(index: number): FormControl | null {
        return this.ctaButtons.at(index).get('url') as FormControl | null;
    }

    addCtaButton(index: number) {
        const newCtaButton = this._createCtaGroupForm();
        this.ctaButtons.insert(index + 1, newCtaButton);
        this._cdr.detectChanges();
    }

    removeCtaButton(index: number) {
        if (index > 0) {
            this.ctaButtons.removeAt(index);
        }
    }

    private _checkIfBlockInError(): void {
        const titleControl = this.titleControl;
        if (!titleControl) {
            return;
        }
        const isTitleInError = !!titleControl?.errors && titleControl.dirty;
        const isAtLeastOneCtaButtonInError = this.ctaButtons.controls.some((control) => {
            const textControl = control.get('text');
            const urlControl = control.get('url');
            return (!!textControl?.errors && textControl.dirty) || (!!urlControl?.errors && urlControl.dirty);
        });

        this.editStoreLocatorPageContext.isBlockInError.set({
            blockType: StoreLocatorPageBlockType.CALL_TO_ACTION,
            isError: isTitleInError || isAtLeastOneCtaButtonInError,
        });
    }

    private _initContentForm(): void {
        const ctaBlockData = this.ctaBlockData();
        if (!ctaBlockData) {
            return;
        }
        if (!this.contentForm) {
            const ctas =
                ctaBlockData.links.length > 0
                    ? ctaBlockData.links
                    : [{ text: this.callToActionsSuggestions()[0].key || '', url: this.callToActionsSuggestions()[0].value || '' }];

            this.contentForm = this._formBuilder.group({
                title: this._formBuilder.control(this.ctaBlockData()?.title ?? '', {
                    validators: [Validators.required, Validators.maxLength(CtaBlockContentFormInputValidation.TITLE_MAX_LENGTH)],
                    nonNullable: true,
                }),
                ctaButtons: this._formBuilder.array<CtaButtonsFormGroup>(
                    ctas.map((ctaButton) => this._createCtaGroupForm(ctaButton.text ?? '', ctaButton.url ?? ''))
                ),
            });

            this.contentForm.valueChanges.subscribe((value: CtaBlockContentFormData) => {
                const ctaData = this.ctaBlockData();
                this._checkIfBlockInError();
                if (ctaData) {
                    this._trackEditContentChanges(value);
                    this.storeLocatorPageState()?.updateBlock({
                        blockType: StoreLocatorPageBlockType.CALL_TO_ACTION,
                        blockData: {
                            ...ctaData,
                            title: value.title,
                            links: value.ctaButtons.map((ctaButton) => ({
                                text: ctaButton.text,
                                url: ctaButton.url,
                            })),
                        },
                    });
                }
            });
        }
    }

    private _createCtaGroupForm(value: string = '', url: string = ''): FormGroup {
        return this._formBuilder.group({
            text: this._formBuilder.control(value, {
                validators: [Validators.required, Validators.maxLength(CtaBlockContentFormInputValidation.CTA_TEXT_MAX_LENGTH)],
                nonNullable: true,
            }),
            url: this._formBuilder.control(url, {
                validators: [Validators.required, UrlValidator()],
                nonNullable: true,
            }),
        });
    }

    private _patchContentForm(): void {
        const ctaBlockData = this.ctaBlockData();
        if (!ctaBlockData) {
            return;
        }
        this.ctaButtons.clear();

        const ctaToPatch =
            ctaBlockData.links.length > 0
                ? ctaBlockData.links
                : [{ text: this.callToActionsSuggestions()[0].key || '', url: this.callToActionsSuggestions()[0].value || '' }];

        ctaToPatch.forEach((ctaButton) => {
            this.ctaButtons.push(this._createCtaGroupForm(ctaButton.text, ctaButton.url));
        });
        this.contentForm.patchValue({
            title: ctaBlockData.title,
        });
        this.contentForm.markAsPristine();
    }

    private _initStyleForm(): void {
        const styleMap = this.getStyleMap([
            StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE,
            StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER,
            StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA,
        ]);

        const ctaBlockWrapperStyle = styleMap[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER] || {};
        const ctaBlockTitleStyle = styleMap[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE] || {};
        const ctaBlockCtaStyle = styleMap[StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA] || {};

        this.styleForm = this._formBuilder.group({
            general: this._formBuilder.group({
                titleColor: this._formBuilder.control(ctaBlockTitleStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(ctaBlockWrapperStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
            buttons: this._formBuilder.group({
                radius: this._formBuilder.control(getRadiusValue(ctaBlockCtaStyle.borderRadius), {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                textColor: this._formBuilder.control(ctaBlockCtaStyle.color, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                backgroundColor: this._formBuilder.control(ctaBlockCtaStyle.backgroundColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
                borderColor: this._formBuilder.control(ctaBlockCtaStyle.borderColor, {
                    validators: [Validators.required],
                    nonNullable: true,
                }),
            }),
        });
        this.styleForm.valueChanges.subscribe((value) => {
            this._updateStyleConfiguration(value as CtaBlockStyleData);
        });
    }

    private _updateStyleConfiguration(value: CtaBlockStyleData): void {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();

        if (organizationStyleConfiguration) {
            const dataToUpdate = mapUpdateStylesConfiguration(organizationStyleConfiguration, [
                {
                    elementId: StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_WRAPPER,
                    data: [
                        {
                            value: value.general.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_TITLE,
                    data: [
                        {
                            value: value.general.titleColor,
                            propertyType: PropertyType.Color,
                        },
                    ],
                },
                {
                    elementId: StoreLocatorRestaurantPageElementIds.CALL_TO_ACTIONS_CTA,
                    data: [
                        {
                            value: value.buttons.textColor,
                            propertyType: PropertyType.Color,
                        },
                        {
                            value: value.buttons.backgroundColor,
                            propertyType: PropertyType.BackgroundColor,
                        },
                        {
                            value: value.buttons.borderColor,
                            propertyType: PropertyType.BorderColor,
                        },
                        {
                            propertyType: PropertyType.BorderRadius,
                            value: value.buttons.radius.toString(),
                        },
                    ],
                },
            ]);
            this.trackEditStyleActivity({
                block: StoreLocatorPageBlockType.CALL_TO_ACTION,
                changes: dataToUpdate,
            });
            organizationStyleConfiguration.updateStyle(dataToUpdate);
        }
    }

    private _trackEditContentChanges(changes: CtaBlockContentFormData): void {
        const ctaBlockData = this.ctaBlockData();
        if (ctaBlockData) {
            if (ctaBlockData.title !== changes.title) {
                this.trackEditContentActivity({
                    block: StoreLocatorPageBlockType.CALL_TO_ACTION,
                    element: StoreLocatorInputType.TITLE,
                });
            }
            if (!this.isElementTrackedForRestaurant(StoreLocatorInputType.CTA, StoreLocatorPageBlockType.CALL_TO_ACTION)) {
                changes.ctaButtons.forEach((ctaButton, index) => {
                    const ctaData = ctaBlockData.links[index];
                    if (ctaData) {
                        if (ctaData.text !== ctaButton.text || ctaData.url !== ctaButton.url) {
                            this.trackEditContentActivity({
                                block: StoreLocatorPageBlockType.CALL_TO_ACTION,
                                element: StoreLocatorInputType.CTA,
                            });
                        }
                    }
                });
            }
        }
    }
}
