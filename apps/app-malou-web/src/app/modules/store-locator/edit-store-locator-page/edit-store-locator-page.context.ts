import { computed, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { isNil } from 'lodash';
import { forkJoin, map, Observable, of, switchMap, throwError } from 'rxjs';

import { UpdateStoreLocatorStorePagesBodyDto } from '@malou-io/package-dto';
import { isNotNil, StoreLocatorJobType } from '@malou-io/package-utils';

import { ToastService } from ':core/services/toast.service';
import { StoreLocatorJobContext } from ':modules/store-locator/contexts/store-locator-jobs.context';
import {
    DEFAULT_PUBLICATION_ESTIMATED_TIME,
    StoreLocatorPageBlockType,
} from ':modules/store-locator/edit-store-locator-page/edit-store-locator-page.interface';
import { StoreLocatorActivityTracker } from ':modules/store-locator/edit-store-locator-page/models/store-locator-activity-tracker';
import { StoreLocatorOrganizationStylesConfiguration } from ':modules/store-locator/edit-store-locator-page/models/store-locator-organization-styles-configuration';
import { StoreLocatorStorePageState } from ':modules/store-locator/edit-store-locator-page/models/store-locator-store-page-state';
import { StoreLocatorOrganizationRestaurant } from ':modules/store-locator/models/store-locator-organization-restaurant';
import { StoreLocatorContext } from ':modules/store-locator/store-locator.context';
import { StoreLocatorService } from ':modules/store-locator/store-locator.service';

@Injectable({
    providedIn: 'root',
})
export class EditStoreLocatorPageContext {
    private readonly _storeLocatorService = inject(StoreLocatorService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _storeLocatorContext = inject(StoreLocatorContext);
    private readonly _storeLocatorJobContext = inject(StoreLocatorJobContext);

    readonly isSavingChanges: WritableSignal<boolean> = signal(false);
    readonly isPublishing: WritableSignal<boolean> = signal(false);

    readonly dialogRef: WritableSignal<any> = signal(null);

    readonly storesPagesStates: WritableSignal<Map<string, StoreLocatorStorePageState>> = signal(new Map());
    readonly organizationRestaurants: WritableSignal<StoreLocatorOrganizationRestaurant[]> = signal([]);
    readonly organizationData: WritableSignal<{ id: string; name: string }> = signal({ id: '', name: '' });
    readonly organizationStyleConfiguration: WritableSignal<StoreLocatorOrganizationStylesConfiguration | null> = signal(null);
    readonly editStoreLocatorActivityTracker: WritableSignal<StoreLocatorActivityTracker | null> = signal(null);

    readonly isBlockInError: WritableSignal<{
        blockType: StoreLocatorPageBlockType;
        isError: boolean;
    }> = signal({
        blockType: StoreLocatorPageBlockType.INFORMATION,
        isError: false,
    });
    readonly currentEditingBlock: WritableSignal<StoreLocatorPageBlockType> = signal(StoreLocatorPageBlockType.INFORMATION);
    readonly descriptionsBlockSelectedItemIndex: WritableSignal<number> = signal(0);

    readonly currentEditingRestaurant: WritableSignal<StoreLocatorOrganizationRestaurant | null> = signal(null);
    readonly selectedRestaurantStorePageState: WritableSignal<StoreLocatorStorePageState | null> = signal(null);

    readonly shouldDisableModal = computed(() => this.isSavingChanges() || this.isPublishing());

    getRestaurantPages(organizationId: string): Observable<StoreLocatorStorePageState[] | null> {
        return this._storeLocatorService
            .getPages(organizationId)
            .pipe(map((res) => res.data.restaurantsPages.map((page) => new StoreLocatorStorePageState(page))));
    }

    updateCurrentEditingRestaurant(restaurant: StoreLocatorOrganizationRestaurant): void {
        const storeLocatorPageUpdate = this.storesPagesStates().get(restaurant.id);
        if (!storeLocatorPageUpdate) {
            console.error('Store locator page update not found for the selected restaurant:');
            return;
        }
        this.selectedRestaurantStorePageState.set(storeLocatorPageUpdate);
        this.currentEditingRestaurant.set(restaurant);
        this.selectedRestaurantStorePageState()?.initBlockUpdateState(this.currentEditingBlock());
    }

    duplicateBlockDataToAll(): void {
        const currentBlockType = this.currentEditingBlock();
        if (!currentBlockType) {
            console.error('No current block type set for duplication.');
            return;
        }

        const isBlockInError = this.isBlockInError();
        if (isBlockInError.isError) {
            console.error(`Cannot duplicate block data: Block type ${isBlockInError.blockType} is in error.`);
            return;
        }

        const blockDataToBeDuplicated = this.selectedRestaurantStorePageState()?.getBlockUpdatedData$(currentBlockType)?.()?.data;
        if (!blockDataToBeDuplicated) {
            console.error(`No data found for block type: ${currentBlockType}`);
            return;
        }

        this.storesPagesStates().forEach((storePageState) => {
            storePageState.updateBlock({
                blockType: currentBlockType,
                blockData: blockDataToBeDuplicated,
            });
        });
    }

    async saveAsDraft(): Promise<void> {
        this.isSavingChanges.set(true);
        const organizationId = this.organizationData().id;

        if (!organizationId) {
            console.error('No organization ID found for the current editing restaurant.');
            this.isSavingChanges.set(false);
            return;
        }

        forkJoin([
            this._updateStoreLocatorStoreContent(organizationId),
            this._updateStoreLocatorOrganizationStyleConfiguration(organizationId),
        ]).subscribe({
            next: (success) => {
                this.isSavingChanges.set(false);
                if (success) {
                    const updatedStyles = this.organizationStyleConfiguration()?.toDto();
                    if (updatedStyles) {
                        const organizationConfiguration = this._storeLocatorContext.storeLocatorOrganizationConfiguration();
                        if (!organizationConfiguration) {
                            return;
                        }
                        organizationConfiguration.updateStyles(updatedStyles.data);
                    }
                    this._toastService.openSuccessToast(this._translateService.instant('store_locator.edit_modal.save_as_draft.success'));
                    this.dialogRef()?.close();
                }
            },
            error: (error) => {
                this.isSavingChanges.set(false);
                console.error('Error saving store locator pages as draft:', error);
                this._toastService.openErrorToast(this._translateService.instant('store_locator.edit_modal.save_as_draft.fail'));
            },
        });
    }

    publish(): void {
        this.isPublishing.set(true);
        const organizationId = this.organizationData().id;

        if (!organizationId) {
            console.error('No organization ID found for the current editing restaurant.');
            this.isPublishing.set(false);
            return;
        }

        const storeLocatorOrganizationConfiguration = this._storeLocatorContext.storeLocatorOrganizationConfiguration();
        if (!storeLocatorOrganizationConfiguration) {
            console.error('No organization style configuration found for the current editing restaurant.');
            this.isPublishing.set(false);
            return;
        }

        forkJoin([
            this._updateStoreLocatorStoreContent(organizationId),
            this._updateStoreLocatorOrganizationStyleConfiguration(organizationId),
        ])
            .pipe(
                switchMap(([updatePagesStatus, updateOrganizationStyleStatus]) => {
                    if (!updatePagesStatus || !updateOrganizationStyleStatus) {
                        return throwError(() => new Error('Failed to update store locator pages or organization styles.'));
                    }
                    const organization = storeLocatorOrganizationConfiguration.organization;
                    this._storeLocatorJobContext.executeJobForOrganization(organization, StoreLocatorJobType.PUBLICATION, {
                        jobEstimatedTime: DEFAULT_PUBLICATION_ESTIMATED_TIME,
                    });
                    return of(true);
                })
            )
            .subscribe({
                next: () => {
                    this.dialogRef()?.close();
                    this.isPublishing.set(false);
                },
                error: (error) => {
                    console.error('Error publishing store locator:', error);
                    this._toastService.openErrorToast(this._translateService.instant('store_locator.publication.publication_error'));
                    this.isPublishing.set(false);
                },
            });
    }

    private _updateStoreLocatorStoreContent(organizationId: string): Observable<boolean> {
        const storesPagesStates = this.storesPagesStates();
        const updateBody: UpdateStoreLocatorStorePagesBodyDto = Array.from(storesPagesStates.values())
            .map((storePageState) => storePageState.toDto())
            .filter(isNotNil);

        if (updateBody.length === 0) {
            return of(true);
        }

        return this._storeLocatorService.updateStoreLocatorStorePages(organizationId, updateBody);
    }

    private _updateStoreLocatorOrganizationStyleConfiguration(organizationId: string): Observable<boolean> {
        const organizationStyleConfiguration = this.organizationStyleConfiguration();
        const updateBody = organizationStyleConfiguration?.toDto();
        if (isNil(updateBody)) {
            return of(true);
        }

        return this._storeLocatorService.updateOrganizationConfigurationStorePages(organizationId, updateBody);
    }
}
