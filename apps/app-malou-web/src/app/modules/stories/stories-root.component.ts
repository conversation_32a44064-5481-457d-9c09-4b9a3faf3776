import { ChangeDetectionStrategy, Component, computed, inject, On<PERSON><PERSON>roy } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';

import { ExperimentationService } from ':core/services/experimentation.service';
import { StoriesComponent } from ':modules/stories/stories.component';
import { StoriesListComponent } from ':modules/stories/v2/components/stories-list/stories-list.component';
import { UpsertStoryModalComponent } from ':modules/stories/v2/components/upsert-story-modal/upsert-story-modal.component';
import {
    UpsertStoryModalProps,
    UpsertStoryModalResult,
} from ':modules/stories/v2/components/upsert-story-modal/upsert-story-modal.interface';
import { DuplicateStoriesService } from ':modules/stories/v2/services/duplicate-stories.service';
import { StoriesContext } from ':modules/stories/v2/stories.context';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { PostSkeletonComponent } from ':shared/components/skeleton/templates/post-skeleton/post-skeleton.component';
import { CustomDialogService, DialogScreenSize } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-stories-root',
    templateUrl: './stories-root.component.html',
    styleUrls: ['./stories-root.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [StoriesComponent, StoriesListComponent, PostSkeletonComponent],
})
export class StoriesRootComponent implements OnDestroy {
    private readonly _storiesContext = inject(StoriesContext);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _duplicateStoriesService = inject(DuplicateStoriesService);
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _router = inject(Router);

    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _isReleaseStoriesV2Enabled = toSignal(this._experimentationService.isFeatureEnabled$('release-stories-v2'), {
        initialValue: false,
    });

    readonly isGrowthbokLoaded = toSignal(this._experimentationService.isLoaded$, { initialValue: false });
    readonly showStoriesListV2 = computed(() => this.isGrowthbokLoaded() && this._isReleaseStoriesV2Enabled());

    ngOnDestroy(): void {
        this._storiesContext.stopPollingStoriesStatus$.next();
    }

    onCreateStory(): void {
        this._openUpsertStoryModal({ disconnectedPlatforms$: this._storiesContext.disconnectedPlatforms$ });
    }

    onUpdateStory(data: { storyId: string; shouldOpenFeedbacks?: boolean }): void {
        this._openUpsertStoryModal({ ...data, disconnectedPlatforms$: this._storiesContext.disconnectedPlatforms$ });
    }

    onDuplicateToOtherRestaurants({
        postRefs,
        isDirectlyAfterUpsertPostModal,
        submitPublicationStatus,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): void {
        this._duplicateStoriesService.duplicate({
            postRefs,
            fromRestaurantId: this._storiesContext.restaurant().id,
            isDirectlyAfterUpsertPostModal,
            submitPublicationStatus,
            onDuplicationSuccess: this._onDuplicationSuccess,
            onDuplicationError: this._onDuplicationError,
            isHere: false,
        });
    }

    private _onDuplicationSuccess = (): void => {
        this._storiesContext.storySelection.unselectAll();
        this._storiesContext.isSelecting.set(false);
        this._customDialogService.closeAll();
    };

    private _onDuplicationError = (error: unknown): void => {
        console.error(error);
        this._storiesContext.storySelection.unselectAll();
        this._storiesContext.isSelecting.set(false);
        this._customDialogService.closeAll();
    };

    private _openUpsertStoryModal(data: UpsertStoryModalProps): void {
        this._customDialogService
            .open<UpsertStoryModalComponent, UpsertStoryModalProps, UpsertStoryModalResult>(
                UpsertStoryModalComponent,
                {
                    width: '100vw',
                    disableClose: false,
                    height: '100vh',
                    data,
                },
                { animateScreenSize: DialogScreenSize.ALL }
            )
            .afterClosed()
            .subscribe((result) => {
                if (result?.story) {
                    // Replace the post in the list and in the feed
                    this._storiesContext.upsertStory(result.story);

                    // Re-fetch the filter options count
                    this._storiesContext.shouldFetchFilterOptionsCount$.next();

                    if (result.duplicateToOtherRestaurants) {
                        this.onDuplicateToOtherRestaurants({
                            postRefs: [{ id: result.story.id }],
                            isDirectlyAfterUpsertPostModal: true,
                            submitPublicationStatus: result.submitPublicationStatus,
                        });
                    }
                }

                if (result?.scrollToStoryId) {
                    this._storiesContext.setFetchUntilStoryIsFound(result.scrollToStoryId);
                    this._storiesContext.fetchStoriesUntilStoryIsFound(result.scrollToStoryId);
                    this._storiesContext.highlightStories([result.scrollToStoryId]);
                }

                const queryParams = this._activatedRoute.snapshot.queryParams;
                if (Object.keys(queryParams).length > 0) {
                    // Remove postId query param from the URL
                    this._router.navigate(['.'], { relativeTo: this._activatedRoute, queryParams: {} });
                }
            });
    }
}
