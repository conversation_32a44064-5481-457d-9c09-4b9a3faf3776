import { StoryDto, UpdateStoryDto } from '@malou-io/package-dto';
import {
    DeviceType,
    PlatformKey,
    PostFeedbacks,
    PostPublicationStatus,
    PublicationErrorCode,
    RemoveMethodsFromEntity,
} from '@malou-io/package-utils';

import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { SocialPostAuthor } from ':modules/posts-v2/social-posts/models/social-post-author';

export type IUpsertStory = RemoveMethodsFromEntity<UpsertStory> & { id: string };

export class UpsertStory implements IUpsertStory {
    id: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    isPublishing: boolean;
    plannedPublicationDate: Date | null;
    medias: EditionMedia[];
    feedbacks: PostFeedbacks | null;
    author?: SocialPostAuthor;
    socialCreatedAt?: Date;
    socialLink?: string;
    mostRecentPublicationErrorCode?: PublicationErrorCode;
    createdFromDeviceType?: DeviceType;
    bindingId?: string;

    constructor(data: IUpsertStory) {
        this.id = data.id;
        this.platformKeys = data.platformKeys;
        this.published = data.published;
        this.isPublishing = data.isPublishing;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.medias = data.medias;
        this.feedbacks = data.feedbacks;
        this.author = data.author;
        this.socialCreatedAt = data.socialCreatedAt;
        this.socialLink = data.socialLink;
        this.mostRecentPublicationErrorCode = data.mostRecentPublicationErrorCode;
        this.createdFromDeviceType = data.createdFromDeviceType;
        this.bindingId = data.bindingId;
    }

    static create(): UpsertStory {
        return new UpsertStory({
            id: '',
            platformKeys: [],
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            plannedPublicationDate: null,
            medias: [],
            feedbacks: null,
        });
    }

    static fromDto(dto: StoryDto): UpsertStory {
        return new UpsertStory({
            id: dto.id,
            platformKeys: dto.platformKeys,
            published: dto.published,
            isPublishing: dto.isPublishing,
            plannedPublicationDate: dto.plannedPublicationDate ? new Date(dto.plannedPublicationDate) : null,
            medias: dto.medias,
            feedbacks: dto.feedbacks
                ? {
                      ...dto.feedbacks,
                      createdAt: new Date(dto.feedbacks.createdAt),
                      updatedAt: new Date(dto.feedbacks.updatedAt),
                      feedbackMessages: dto.feedbacks.feedbackMessages.map((message) => ({
                          ...message,
                          author: {
                              ...message.author,
                              userId: message.author.userId ?? undefined,
                              profilePictureUrl: message.author.profilePictureUrl ?? undefined,
                          },
                          createdAt: new Date(message.createdAt),
                          updatedAt: new Date(message.updatedAt),
                          publishedAt: message.publishedAt ? new Date(message.publishedAt) : undefined,
                          lastUpdatedAt: message.lastUpdatedAt ? new Date(message.lastUpdatedAt) : undefined,
                      })),
                      participants: dto.feedbacks.participants.map((participant) => ({
                          ...participant,
                          participant: {
                              ...participant.participant,
                              role: participant.participant.role ?? undefined,
                              userId: participant.participant.userId ?? undefined,
                              lastname: participant.participant.lastname ?? undefined,
                          },
                      })),
                  }
                : null,
            author: dto.author ? SocialPostAuthor.fromDto(dto.author) : undefined,
            socialCreatedAt: dto.socialCreatedAt ? new Date(dto.socialCreatedAt) : undefined,
            mostRecentPublicationErrorCode: dto.mostRecentPublicationErrorCode,
            socialLink: dto.socialLink,
            createdFromDeviceType: dto.createdFromDeviceType ?? undefined,
            bindingId: dto.bindingId,
        });
    }

    toInterface(): IUpsertStory {
        return {
            id: this.id,
            platformKeys: this.platformKeys,
            published: this.published,
            isPublishing: this.isPublishing,
            plannedPublicationDate: this.plannedPublicationDate,
            medias: this.medias,
            feedbacks: this.feedbacks,
            author: this.author,
            socialCreatedAt: this.socialCreatedAt,
            mostRecentPublicationErrorCode: this.mostRecentPublicationErrorCode,
            socialLink: this.socialLink,
            createdFromDeviceType: this.createdFromDeviceType,
            bindingId: this.bindingId,
        };
    }

    toUpdateStoryDto(): UpdateStoryDto {
        return {
            id: this.id,
            platformKeys: this.platformKeys,
            published: this.published,
            isPublishing: this.isPublishing,
            plannedPublicationDate: this.plannedPublicationDate ? this.plannedPublicationDate.toISOString() : new Date().toISOString(),
            medias: this.medias,
            feedbackId: this.feedbacks ? this.feedbacks.id : null,
            author: this.author,
        };
    }

    isEmpty(): boolean {
        return this.published === PostPublicationStatus.DRAFT && this.platformKeys.length === 0;
    }
}
