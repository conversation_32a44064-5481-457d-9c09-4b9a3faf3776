import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { catchError, map, Observable, of, tap } from 'rxjs';

import { getFeatureFlaggedPlatforms, getPlatformKeysWithStories, HeapEventName, PlatformKey } from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { HeapService } from ':core/services/heap.service';
import { ToastService } from ':core/services/toast.service';
import { StoryToDuplicate } from ':modules/stories/v2/models/story-to-duplicate';
import { StoriesService } from ':modules/stories/v2/stories.service';
import { selectUserInfos } from ':modules/user/store/user.selectors';
import {
    DuplicateStoryPreviewModalComponent,
    DuplicateStoryPreviewModalSubmitData,
} from ':shared/components/duplicate-post-preview-modal/duplicate-story-preview-modal/duplicate-story-preview-modal.component';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import {
    RestaurantsSelectionComponent,
    RestaurantsSelectionData,
} from ':shared/components/restaurants-selection/restaurants-selection.component';
import { StepperModalComponent } from ':shared/components/stepper-modal/stepper-modal.component';
import { Step } from ':shared/interfaces/step.interface';
import { Restaurant } from ':shared/models';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Injectable({
    providedIn: 'root',
})
export class DuplicateStoriesService {
    private readonly _storiesService = inject(StoriesService);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _heapService = inject(HeapService);
    private readonly _store = inject(Store);

    duplicate({
        postRefs,
        fromRestaurantId,
        onDuplicationSuccess,
        onDuplicationError,
        isHere,
        isDirectlyAfterUpsertPostModal,
        submitPublicationStatus,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        fromRestaurantId: string;
        onDuplicationSuccess: () => void;
        onDuplicationError: (error: unknown) => void;
        isHere: boolean;
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): void {
        if (isHere) {
            this._duplicateHere({
                postRefs,
                fromRestaurantId,
                onDuplicationSuccess,
                onDuplicationError,
            });
        } else {
            this._duplicateToOtherRestaurants({
                postRefs,
                fromRestaurantId,
                isDirectlyAfterUpsertPostModal,
                submitPublicationStatus,
                onDuplicationSuccess,
                onDuplicationError,
            });
        }
    }

    private _duplicateHere({
        postRefs,
        fromRestaurantId,
        onDuplicationSuccess,
        onDuplicationError,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        fromRestaurantId: string;
        onDuplicationSuccess: () => void;
        onDuplicationError: (error: unknown) => void;
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): void {
        this._storiesService
            .duplicateStories$({
                postRefs,
                fromRestaurantId,
                restaurantIds: [fromRestaurantId],
            })
            .subscribe({
                next: () => {
                    onDuplicationSuccess();
                },
                error: (error) => {
                    onDuplicationError(error);
                },
            });
    }

    private _duplicateToOtherRestaurants({
        postRefs,
        fromRestaurantId,
        onDuplicationSuccess,
        onDuplicationError,
        isDirectlyAfterUpsertPostModal,
        submitPublicationStatus,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        fromRestaurantId: string;
        onDuplicationSuccess: () => void;
        onDuplicationError: (error: unknown) => void;
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): void {
        this._storiesService.getStoriesToDuplicate$(postRefs).subscribe((result) => {
            const storiesToDuplicate = result.data.map((storyToDuplicate) => StoryToDuplicate.fromDto(storyToDuplicate));
            const steps = this._getStepsForDuplication({
                storiesToDuplicate,
                fromRestaurantId,
                isDirectlyAfterUpsertPostModal,
                submitPublicationStatus,
            });

            const platformsForStories = this._getPlatformKeysWithStories();

            const initialData: RestaurantsSelectionData = {
                skipOwnRestaurant: true,
                withoutBrandBusiness: false,
                selectedRestaurants: [],
                hasPlatform: platformsForStories,
                disableRestaurantWithoutMapstrPremium: false,
            };

            this._customDialogService.open(StepperModalComponent, {
                width: 'unset',
                height: 'unset',
                panelClass: 'malou-dialog-panel--without-border-radius',
                data: {
                    steps,
                    initialData,
                    fullScreenStepIndexes: [], // steps.map((_, index) => index).filter((index) => index !== 0),
                    noHeaderStepIndexes: steps.map((_, index) => index).filter((index) => index !== 0),
                    sharedData: {
                        storiesToDuplicate,
                    },
                    title: this._translateService.instant('duplicate_to_restaurants_dialog.title'),
                    onSuccess: (_data: { selectedRestaurants: Restaurant[]; index: number }) => {
                        onDuplicationSuccess();
                    },
                    onError: (error: unknown) => {
                        onDuplicationError(error);
                    },
                    shouldDisplayConfirmationCloseModalAfterClosed: true,
                    malouDialogBodyCustomStyle: {
                        padding: '0px',
                        margin: '0px',
                    },
                },
            });
        });
    }

    private _getStepsForDuplication({
        storiesToDuplicate,
        fromRestaurantId,
        skipRestaurantSelection = false,
        isDirectlyAfterUpsertPostModal = false,
        submitPublicationStatus,
    }: {
        storiesToDuplicate: StoryToDuplicate[];
        fromRestaurantId: string;
        skipRestaurantSelection?: boolean;
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): Step[] {
        const storyCount = storiesToDuplicate.length;
        return [
            ...(skipRestaurantSelection
                ? []
                : [
                      {
                          component: RestaurantsSelectionComponent,
                          subtitle: this._translateService.instant('duplicate_to_restaurants_dialog.subtitle'),
                          primaryButtonText: this._translateService.instant('common.next'),
                          nextFunction$: (data: RestaurantsSelectionData) =>
                              of({
                                  selectedRestaurants: data.selectedRestaurants ?? [],
                                  index: 0,
                              }),
                      },
                  ]),
            ...storiesToDuplicate.map((post, index) => ({
                component: DuplicateStoryPreviewModalComponent,
                primaryButtonText:
                    storyCount > 1 && index < storyCount - 1
                        ? this._translateService.instant('common.duplicate_and_go_next') + ` (${index + 1}/${storyCount})`
                        : this._translateService.instant('common.duplicate'),
                hideSecondaryButton: index > 0,
                nextFunction$: (
                    data: DuplicateStoryPreviewModalSubmitData[]
                ): Observable<{ selectedRestaurants: Restaurant[]; index: number }> =>
                    this._storiesService
                        .duplicateStories$({
                            fromRestaurantId,
                            restaurantIds: data.map((d) => d.restaurant.id),
                            postRefs:
                                isDirectlyAfterUpsertPostModal && submitPublicationStatus === SubmitPublicationStatus.NOW
                                    ? /**
                                       * During publication, the post is deleted, so passing its ID for the duplication is a bad idea.
                                       * Instead, we pass the `bindingId`, which is shared across versions of the same post.
                                       * This is a special-case workaround with a known drawback: the API may find multiple
                                       * posts with the same `bindingId` and will select one somewhat arbitrarily.
                                       * While not ideal, this is the most reliable option available for this scenario.
                                       */
                                      [{ bindingId: post.bindingId }]
                                    : [{ id: post.id }],
                            customFields: data.map((d) => ({
                                restaurantId: d.restaurant.id,
                                plannedPublicationDate: d.plannedPublicationDate,
                                platformKeys: d.platformKeys,
                                published: d.status,
                            })),
                        })
                        .pipe(
                            tap(() => {
                                const currentUser = this._store.selectSignal(selectUserInfos);
                                const heapEventName: HeapEventName =
                                    storyCount > 1
                                        ? HeapEventName.SOCIAL_POSTS_DUPLICATE_TO_OTHER_RESTAURANTS_BULK_V2
                                        : HeapEventName.SOCIAL_POSTS_DUPLICATE_TO_OTHER_RESTAURANTS_V2;

                                this._heapService.track(heapEventName, {
                                    restaurantIds: data.map((d) => d.restaurant.id),
                                    postBindingId: post.bindingId,
                                    userId: currentUser()?.id,
                                    email: currentUser()?.email,
                                });
                                this._toastService.openSuccessToast(this._translateService.instant('social_posts.success_duplicate_post'));
                            }),
                            map(() => ({ selectedRestaurants: data.map((d) => d.restaurant), index: index + 1 })),
                            catchError((error) => {
                                console.error('Error while duplicating post', error);
                                this._toastService.openErrorToast(this._translateService.instant('social_posts.error_duplicate_post'));
                                return of({ selectedRestaurants: data.map((d) => d.restaurant), index: index + 1 });
                            })
                        ),
            })),
        ];
    }

    private _getPlatformKeysWithStories(): PlatformKey[] {
        const allPlatformKeys = getPlatformKeysWithStories();
        return this._getEnabledPlatformKeys(allPlatformKeys);
    }

    private _getEnabledPlatformKeys(platformKeys: PlatformKey[]): PlatformKey[] {
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
        const enabledPlatformKeys = platformKeys.filter((platformKey) => {
            const ffPlatform = featureFlaggedPlatforms.find((platform) => platform.key === platformKey);
            return !ffPlatform || (ffPlatform.featureFlagKey && this._experimentationService.isFeatureEnabled(ffPlatform.featureFlagKey));
        });
        return enabledPlatformKeys;
    }
}
