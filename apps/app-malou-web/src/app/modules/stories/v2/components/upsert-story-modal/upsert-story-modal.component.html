<div class="flex h-full flex-col" [id]="CREATE_STORY_MODAL_HTML_ID">
    <ng-container [ngTemplateOutlet]="headerTemplate"></ng-container>

    <div class="flex min-h-0 grow lg:flex-col">
        <div class="flex min-w-0 grow flex-col">
            <div class="flex min-h-0 grow lg:flex-col">
                <app-duplicate-and-select-stories-platforms
                    class="w-[270px] shrink-0 lg:w-full"
                    [isReadonly]="isReadonly()"></app-duplicate-and-select-stories-platforms>

                <app-stories-content-form class="min-w-0 grow" [isReadonly]="isReadonly()"></app-stories-content-form>
            </div>

            <app-upsert-story-modal-footer
                class="lg:hidden"
                [isSubmitting]="isSubmitting()"
                [isDisabled]="isLoadingPost()"
                (cancel)="onCancel()"
                (saveStories)="onSaveStories($event)"></app-upsert-story-modal-footer>
        </div>

        <app-stories-previews-feed-notes
            class="w-[400px] shrink-0 lg:w-full"
            [isReadonly]="isReadonly()"
            [shouldOpenFeedbacks]="shouldOpenFeedbacks()"></app-stories-previews-feed-notes>

        <app-upsert-story-modal-footer
            class="hidden lg:block"
            [isSubmitting]="isSubmitting()"
            [isDisabled]="isLoadingPost()"
            (cancel)="onCancel()"
            (saveStories)="onSaveStories($event)"></app-upsert-story-modal-footer>
    </div>
</div>

<ng-template #headerTemplate>
    <div class="flex items-center justify-between border-b border-malou-color-border-primary px-6 py-5">
        <div class="malou-text-18--semibold text-malou-color-text-1">
            {{
                storyId()
                    ? ('stories.upsert_stories_modal.title.update' | translate)
                    : ('stories.upsert_stories_modal.title.create' | translate)
            }}
        </div>

        <button
            class="malou-btn-icon !rounded-full !bg-malou-color-background-dark"
            data-testid="close-upsert-story-btn"
            mat-icon-button
            [disabled]="isReadonly()"
            (click)="close()">
            <mat-icon color="primary" [svgIcon]="SvgIcon.CROSS"></mat-icon>
        </button>
    </div>
</ng-template>
