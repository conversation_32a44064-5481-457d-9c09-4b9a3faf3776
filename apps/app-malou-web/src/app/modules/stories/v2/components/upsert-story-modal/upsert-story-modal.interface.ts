import { BehaviorSubject } from 'rxjs';

import { StoryItem } from ':modules/stories/v2/models/story-item';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { Platform } from ':shared/models';

export interface UpsertStoryModalProps {
    storyId?: string;
    shouldOpenFeedbacks?: boolean;
    date?: Date;
    disconnectedPlatforms$: BehaviorSubject<Platform[]>;
}

export interface UpsertStoryModalResult {
    story: StoryItem | null;
    scrollToStoryId?: string;
    duplicateToOtherRestaurants: boolean;
    submitPublicationStatus?: SubmitPublicationStatus;
}
