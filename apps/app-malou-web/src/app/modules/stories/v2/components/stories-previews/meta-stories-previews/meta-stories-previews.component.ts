import { DatePipe, LowerCasePipe, Ng<PERSON>lass, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, input, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, filter, interval, map, of, startWith, switchMap, timer } from 'rxjs';

import { isNotNil, MediaType, PostPublicationStatus, TimeInMilliseconds } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';

@Component({
    selector: 'app-meta-stories-previews',
    templateUrl: './meta-stories-previews.component.html',
    styleUrls: ['./meta-stories-previews.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatIconModule,
        TranslateModule,
        MalouSpinnerComponent,
        ApplySelfPurePipe,
        DatePipe,
        EnumTranslatePipe,
        ImagePathResolverPipe,
        IncludesPipe,
        LowerCasePipe,
        PluralTranslatePipe,
    ],
    providers: [ImagePathResolverPipe],
})
export class MetaStoriesPreviewsComponent implements OnInit {
    readonly stories = input.required<StoryItem[]>();
    readonly accountName = input<string>();
    readonly profilePictureUrl = input<string>();
    readonly showStatus = input<boolean>(true);

    private readonly _imagePathResolverPipe = inject(ImagePathResolverPipe);
    private readonly _destroyRef = inject(DestroyRef);

    readonly currentStoryIdx = signal(0);

    readonly currentStory = computed(() => {
        const currentStoryIdx = this.currentStoryIdx();
        const stories = this.stories();
        return currentStoryIdx < stories.length ? stories[currentStoryIdx] : undefined;
    });

    readonly _storyPlayDate = signal<Date>(new Date());
    readonly _interval$ = interval(50 * TimeInMilliseconds.MILLISECOND).pipe(startWith(0), takeUntilDestroyed(this._destroyRef));
    readonly _interval = toSignal(this._interval$);

    readonly progress = computed(() => {
        const MAX_PROGRESS = 100;

        const currentStory = this.currentStory();
        if (!currentStory) {
            return 0;
        }
        const mediaDuration = currentStory.getMediaDuration() * TimeInMilliseconds.SECOND;
        const storyPlayDate = this._storyPlayDate();
        if (isNotNil(this._interval())) {
            const now = new Date();
            const progressTime = now.getTime() - storyPlayDate.getTime();
            const progressPercentage = Math.round((progressTime / mediaDuration) * 100);
            return Math.min(progressPercentage, MAX_PROGRESS);
        }
        return 0;
    });
    readonly progressPercentage = computed(() => {
        const progress = this.progress();
        return `${progress}%`;
    });

    readonly SvgIcon = SvgIcon;
    readonly MediaType = MediaType;
    readonly PostPublicationStatus = PostPublicationStatus;

    private readonly _mediaDelay$ = new BehaviorSubject<number>(0);
    private readonly _DEFAULT_DELAY = 5000; // in ms

    constructor() {
        effect(() => {
            const currentStory = this.currentStory();
            this._storyPlayDate.set(new Date());
            if (!currentStory) {
                return;
            }
            if (currentStory.media?.type === MediaType.VIDEO) {
                this._mediaDelay$.next(0);
            } else {
                this._mediaDelay$.next(this._DEFAULT_DELAY);
            }
        });
    }

    ngOnInit(): void {
        this._mediaDelay$
            .pipe(
                // emit 0 to pause timer
                filter(Boolean),
                switchMap((delay) => of(delay).pipe(switchMap((time) => timer(time).pipe(map(() => time)))))
            )
            .subscribe({
                next: (res) => {
                    // emit 1 when user slide manually. currentStoryIdx already updated
                    if (res !== 1) {
                        this.currentStoryIdx.update((oldValue) => (oldValue + 1) % this.stories().length);
                    }
                },
            });
    }

    onVideoEnd(): void {
        this._mediaDelay$.next(2);
    }

    previousMedia(): void {
        if (this.currentStoryIdx() <= 0) {
            this.currentStoryIdx.set(this.stories().length - 1);
        } else {
            this.currentStoryIdx.update((oldValue) => oldValue - 1);
        }
        this._mediaDelay$.next(1);
    }

    nextMedia(): void {
        this.currentStoryIdx.update((oldValue) => (oldValue + 1) % this.stories().length);
        this._mediaDelay$.next(1);
    }

    onImgError(event: Event): void {
        (event.target as HTMLImageElement).src = this._imagePathResolverPipe.transform('default_post');
    }
}
