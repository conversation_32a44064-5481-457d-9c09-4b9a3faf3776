import { CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList } from '@angular/cdk/drag-drop';
import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';

import { StoryMediaComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/story-media/story-media.component';
import { UpsertStoryContext } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.context';
import { AddMediaComponent } from ':shared/components/posts-v2/medias/add-media/add-media.component';
import { IMAGE_MIME_TYPES, VIDEO_MIME_TYPES } from ':shared/components/posts-v2/medias/posts-v2-media.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-stories-content-form',
    templateUrl: './stories-content-form.component.html',
    styleUrls: ['./stories-content-form.component.scss'],
    imports: [CdkDropList, CdkDrag, CdkDragHandle, MatIconModule, AddMediaComponent, StoryMediaComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoriesContentFormComponent {
    readonly isReadonly = input.required<boolean>();

    private readonly _upsertStoryContext = inject(UpsertStoryContext);

    readonly acceptAttribute = [...IMAGE_MIME_TYPES, ...VIDEO_MIME_TYPES];

    readonly medias = this._upsertStoryContext.upsertStoryState.upsertStory.medias;

    readonly SvgIcon = SvgIcon;

    onImportMediaFromGallery(): void {
        // TODO stories-v2 in a future PR
    }

    onImportFromFile(_event: Event): void {
        // TODO stories-v2 in a future PR
    }

    drop(event: CdkDragDrop<string[]>): void {
        this._upsertStoryContext.reorderMedias(event.previousIndex, event.currentIndex);
    }
}
