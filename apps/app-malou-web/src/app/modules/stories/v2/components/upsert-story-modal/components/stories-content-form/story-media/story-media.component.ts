import { LowerCasePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateService } from '@ngx-translate/core';

import { MediaType } from '@malou-io/package-utils';

import { SocialPostMediaItemComponent } from ':modules/posts-v2/social-posts/components/social-posts-list/social-post-item/social-post-media-item/social-post-media-item.component';
import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { SocialPostMedia } from ':modules/posts-v2/social-posts/models/social-post-media';
import { DEFAULT_PHOTO_DURATION_IN_SECONDS } from ':modules/stories/v2/models/utils';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';

@Component({
    selector: 'app-story-media',
    templateUrl: './story-media.component.html',
    styleUrls: ['./story-media.component.scss'],
    imports: [MatButtonModule, MatIconModule, SocialPostMediaItemComponent, EnumTranslatePipe, LowerCasePipe, PluralTranslatePipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoryMediaComponent {
    readonly isReadonly = input.required<boolean>();
    readonly media = input.required<EditionMedia>();

    readonly editMedia = output<void>();
    readonly duplicateMedia = output<void>();
    readonly deleteMedia = output<void>();

    readonly _translateService = inject(TranslateService);

    readonly mediaDuration = computed(() => {
        const media = this.media();
        return media.type === MediaType.VIDEO ? (media.duration ?? 0) : DEFAULT_PHOTO_DURATION_IN_SECONDS;
    });

    readonly socialPostMedia = computed(() => {
        const media = this.media();
        return SocialPostMedia.fromEditionMedia(media);
    });

    readonly SvgIcon = SvgIcon;

    onEditMedia(): void {
        this.editMedia.emit();
    }

    onDuplicateMedia(): void {
        this.duplicateMedia.emit();
    }

    onDeleteMedia(): void {
        this.deleteMedia.emit();
    }
}
