<div class="h-full border-r border-malou-color-border-primary bg-malou-color-background-light lg:flex lg:justify-between">
    <div class="flex flex-col gap-4 border-b border-malou-color-border-primary px-6 py-5 lg:w-1/2 lg:px-10">
        <div class="malou-text-14--semibold text-malou-color-text-1">
            {{ 'stories.upsert_stories_modal.duplicate_and_select_platforms.platforms' | translate }}
        </div>

        <div class="flex flex-col gap-2">
            @for (platformKey of storyPlatformKeys(); track platformKey) {
                <app-select-platform
                    [platformKey]="platformKey"
                    [checked]="isSelected | applyPure: platformKey : selectedStoryPlatforms()"
                    [tooltip]="
                        !(isConnected | applyPure: platformKey : connectedStoryPlatformKeys())
                            ? ('stories.upsert_stories_modal.duplicate_and_select_platforms.not_connected' | translate)
                            : ''
                    "
                    [disabled]="
                        !(isConnected | applyPure: platformKey : connectedStoryPlatformKeys()) || isAlreadyPublished() || isReadonly()
                    "
                    [testId]="'upsert_stories_modal_select_platform_btn_' + platformKey"
                    (selectChange)="onSelectChange(platformKey, $event)"></app-select-platform>
            }
        </div>
    </div>

    <div class="flex flex-col gap-4 border-b border-malou-color-border-primary px-6 py-5 lg:w-1/2 lg:px-10">
        <div class="malou-text-14--semibold text-malou-color-text-1">
            {{ 'stories.upsert_stories_modal.duplicate_and_select_platforms.duplicate' | translate }}
        </div>

        <div class="flex flex-col gap-2">
            <div class="flex items-center justify-between">
                <div class="malou-text-12--regular text-malou-color-text-2" [ngClass]="{ 'opacity-50': isReadonly() }">
                    {{ 'stories.upsert_stories_modal.duplicate_and_select_platforms.duplicate_to_other_restaurants' | translate }}
                </div>

                <app-slide-toggle
                    [id]="'tracking_upsert_stories_modal_duplicate_to_other_restaurants'"
                    [checked]="duplicateToOtherRestaurants()"
                    [disabled]="isReadonly()"
                    [testId]="'upsert_stories_modal_duplicate_to_other_restaurants'"
                    (onToggle)="onToggleDuplicateToOtherRestaurants($event)"></app-slide-toggle>
            </div>
        </div>
    </div>
</div>
