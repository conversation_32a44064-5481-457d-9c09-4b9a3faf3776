@switch (selectedPreviewPlatform()?.platformKey) {
    @case (PlatformKey.INSTAGRAM) {
        <app-instagram-stories-previews
            [stories]="instagramStories()"
            [accountName]="selectedPreviewPlatform()?.username"
            [profilePictureUrl]="selectedPreviewPlatform()?.profilePictureUrl"
            [showStatus]="showStatus()"></app-instagram-stories-previews>
    }
    @case (PlatformKey.FACEBOOK) {
        <app-facebook-stories-previews
            [stories]="facebookStories()"
            [accountName]="selectedPreviewPlatform()?.username"
            [profilePictureUrl]="selectedPreviewPlatform()?.profilePictureUrl"
            [showStatus]="showStatus()"></app-facebook-stories-previews>
    }
}
