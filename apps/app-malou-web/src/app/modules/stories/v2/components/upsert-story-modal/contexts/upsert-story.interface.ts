import { IGAccount } from '@malou-io/package-utils';

import { IUpsertStory } from ':modules/stories/v2/models/upsert-story';
import { Platform } from ':shared/models';

export interface UpsertStoryState {
    upsertStory: IUpsertStory;
    isAutoSaving: boolean;
    isSubmitting: boolean;
    isLoadingStories: boolean;
    duplicateToOtherRestaurants: boolean;
    connectedSocialPlatforms: Platform[];
    userTagsHistory: { username: string; count: number; igAccount: IGAccount }[];
}
