<div class="group flex items-center justify-between gap-x-2">
    <div class="flex items-center gap-x-5">
        <div class="h-[65px] w-[65px] overflow-hidden rounded-[10px]">
            <app-social-post-media-item [media]="socialPostMedia()" [isReadonly]="isReadonly()"></app-social-post-media-item>
        </div>

        <div class="flex flex-col gap-2">
            <div class="malou-text-12--regular italic text-malou-color-text-2">
                @let duration = mediaDuration();
                {{ media().type | enumTranslate: 'media_type' }} - {{ duration }}
                {{ 'common.seconds' | pluralTranslate: duration | lowercase }}
            </div>
        </div>
    </div>

    <div class="invisible flex items-center gap-x-2 group-hover:visible">
        <button class="malou-btn-icon--alt" mat-icon-button [disabled]="isReadonly()" (click)="onEditMedia()">
            <mat-icon class="icon-btn" color="primary" [svgIcon]="SvgIcon.EDIT"></mat-icon>
        </button>

        <button class="malou-btn-icon--alt" mat-icon-button [disabled]="isReadonly()" (click)="onDuplicateMedia()">
            <mat-icon class="icon-btn" color="primary" [svgIcon]="SvgIcon.DUPLICATE"></mat-icon>
        </button>

        <button class="malou-btn-icon--alt" mat-icon-button [disabled]="isReadonly()" (click)="onDeleteMedia()">
            <mat-icon class="icon-btn" color="warn" [svgIcon]="SvgIcon.TRASH"></mat-icon>
        </button>
    </div>
</div>
