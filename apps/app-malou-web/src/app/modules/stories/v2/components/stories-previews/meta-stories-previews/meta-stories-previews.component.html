<div class="relative flex aspect-9/16 h-full w-full justify-center bg-malou-color-background-dark">
    <div class="absolute left-2 top-2 flex w-[96%] gap-x-2">
        @for (story of stories(); track story.id; let i = $index) {
            <div
                class="h-[3px] grow rounded-[4px]"
                [ngClass]="{ 'bg-white': i < currentStoryIdx(), 'bg-white/[.35]': i >= currentStoryIdx() }">
                @if (i === currentStoryIdx()) {
                    <div class="h-full rounded-full bg-white" [style]="{ width: progressPercentage() }"></div>
                }
            </div>
        }
    </div>

    <div class="absolute top-[20px] flex w-full justify-between px-4">
        <div class="flex items-center gap-x-2 text-white">
            @if (accountName(); as accountName) {
                <img class="h-8 w-8 rounded-full" [src]="profilePictureUrl() ?? ('default_logo' | imagePathResolver)" />
                <span class="malou-text-14--semibold">{{ accountName }}</span>
            }
            @if (currentStory(); as currentStory) {
                @if (currentStory | applySelfPure: 'isActive') {
                    @let remainingHours = currentStory | applySelfPure: 'getRemainingHours';
                    <span class="malou-text-14--regular">
                        {{ remainingHours }} {{ 'common.hours' | pluralTranslate: remainingHours | lowercase }}
                    </span>
                }
            }
        </div>

        <div class="flex items-center gap-x-1">
            @if (currentStory(); as currentStory) {
                @if (([PostPublicationStatus.DRAFT, PostPublicationStatus.PENDING] | includes: currentStory.published) && showStatus()) {
                    <ng-container
                        [ngTemplateOutlet]="storyStatusTemplate"
                        [ngTemplateOutletContext]="{
                            date: currentStory | applySelfPure: 'getPostDate',
                            published: currentStory.published,
                            isActive: currentStory | applySelfPure: 'isActive',
                            isPublishing: currentStory.isPublishing,
                        }"></ng-container>
                }
            }

            <mat-icon class="!h-4 !w-4 !fill-white" color="white" [svgIcon]="SvgIcon.ELLIPSIS"></mat-icon>
        </div>
    </div>

    @if (!stories().length) {
        <ng-container
            [ngTemplateOutlet]="imageMedia"
            [ngTemplateOutletContext]="{ url: 'default_post' | imagePathResolver, objectCover: false }"></ng-container>
    } @else {
        @if (currentStory()?.media; as currentStoryMedia) {
            @switch (currentStoryMedia?.type) {
                @case (MediaType.PHOTO) {
                    <ng-container
                        [ngTemplateOutlet]="imageMedia"
                        [ngTemplateOutletContext]="{ url: currentStoryMedia.url, objectCover: true }"></ng-container>
                }
                @case (MediaType.VIDEO) {
                    <ng-container [ngTemplateOutlet]="videoMedia"></ng-container>
                }
            }
        } @else {
            <ng-container
                [ngTemplateOutlet]="imageMedia"
                [ngTemplateOutletContext]="{ url: 'default_post' | imagePathResolver, objectCover: false }"></ng-container>
        }
    }

    @if (stories().length > 1) {
        <div class="absolute left-0 top-[45%] z-10 flex w-full translate-y-[50%] transform px-3">
            <mat-icon
                class="!h-6 !w-6 cursor-pointer !text-malou-color-text-white"
                [svgIcon]="SvgIcon.CHEVRON_LEFT"
                (click)="previousMedia()"></mat-icon>
            <mat-icon
                class="!ml-auto !h-6 !w-6 cursor-pointer !text-malou-color-text-white"
                [svgIcon]="SvgIcon.CHEVRON_RIGHT"
                (click)="nextMedia()"></mat-icon>
        </div>
    }
</div>

<ng-template let-url="url" let-objectCover="objectCover" #imageMedia>
    <img
        class="aspect-9/16 min-w-full"
        [ngClass]="{ 'object-cover': objectCover, 'object-contain': !objectCover }"
        [src]="url"
        (error)="onImgError($event)" />
</ng-template>

<ng-template #videoMedia>
    <video class="aspect-9/16 min-w-full object-cover" id="videoMedia" autoplay [muted]="true" (ended)="onVideoEnd()">
        <source type="video/mp4" [src]="currentStory()?.media?.url" />
    </video>
</ng-template>

<ng-template let-date="date" let-published="published" let-isActive="isActive" let-isPublishing="isPublishing" #storyStatusTemplate>
    <div
        class="malou-text-10--semibold flex h-6 items-center gap-x-[2px] rounded-[3px] px-2 text-white"
        [ngClass]="{
            'bg-malou-color-background-warning-darker/60': published === PostPublicationStatus.PENDING && !isPublishing,
            'bg-malou-color-background-lavender-light': published === PostPublicationStatus.PENDING && isPublishing,
            'bg-malou-color-primary/60': published === PostPublicationStatus.DRAFT,
        }">
        <span>
            @if (published === PostPublicationStatus.PENDING && isPublishing) {
                <app-malou-spinner size="xs" color="#AC32B7"></app-malou-spinner>
                <div>{{ 'social_post.is_publishing' | translate }}</div>
            } @else {
                {{ published | enumTranslate: 'publication_status' }}
            }
        </span>

        <span>{{ date | date: 'shortDate' }} - {{ date | date: 'shortTime' }}</span>
    </div>
</ng-template>
