import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { intersection } from 'lodash';
import { catchError, map, Observable, of, tap } from 'rxjs';

import {
    getFeatureFlaggedPlatforms,
    getPlatformKeysWithReel,
    getSeoPlatformKeysWithPost,
    getSocialPlatformKeysWithPost,
    HeapEventName,
    PlatformKey,
    PostSource,
} from '@malou-io/package-utils';

import { ExperimentationService } from ':core/services/experimentation.service';
import { HeapService } from ':core/services/heap.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { PostToDuplicate } from ':modules/posts-v2/social-posts/models/post-to-duplicate';
import { SocialPostsV2Service } from ':modules/posts-v2/social-posts/social-posts.service';
import { selectUserInfos } from ':modules/user/store/user.selectors';
import {
    DuplicateSocialPostPreviewModalSubmitData,
    DuplicateSocialPostWithTextGenerationInputData,
    DuplicateSocialPostWithTextGenerationPreviewModalV2Component,
} from ':shared/components/duplicate-post-preview-modal/duplicate-social-post-with-text-generation-preview-modal-v2/duplicate-social-post-with-text-generation-preview-modal-v2.component';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import {
    RestaurantsSelectionComponent,
    RestaurantsSelectionData,
} from ':shared/components/restaurants-selection/restaurants-selection.component';
import { StepperModalComponent } from ':shared/components/stepper-modal/stepper-modal.component';
import { Step } from ':shared/interfaces/step.interface';
import { Restaurant } from ':shared/models';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Injectable({
    providedIn: 'root',
})
export class DuplicateSocialPostsService {
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _socialPostsService = inject(SocialPostsV2Service);
    private readonly _experimentationService = inject(ExperimentationService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _heapService = inject(HeapService);
    private readonly _store = inject(Store);

    duplicate({
        postRefs,
        postDestination,
        fromRestaurantId,
        onDuplicationSuccess,
        onDuplicationError,
        isHere,
        isDirectlyAfterUpsertPostModal,
        submitPublicationStatus,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        postDestination: PostSource;
        fromRestaurantId: string;
        onDuplicationSuccess: () => void;
        onDuplicationError: (error: unknown) => void;
        isHere: boolean;
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): void {
        if (isHere) {
            this._duplicateHere({
                postRefs,
                postDestination,
                fromRestaurantId,
                onDuplicationSuccess,
                onDuplicationError,
                isDirectlyAfterUpsertPostModal,
                submitPublicationStatus,
            });
        } else {
            this._duplicateToOtherRestaurants({
                postRefs,
                postDestination,
                fromRestaurantId,
                onDuplicationSuccess,
                onDuplicationError,
            });
        }
    }

    private _duplicateHere({
        postRefs,
        postDestination,
        fromRestaurantId,
        onDuplicationSuccess,
        onDuplicationError,
        isDirectlyAfterUpsertPostModal = false,
        submitPublicationStatus,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        fromRestaurantId: string;
        postDestination: PostSource;
        onDuplicationSuccess: () => void;
        onDuplicationError: (error: unknown) => void;
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): void {
        if (postDestination === PostSource.SOCIAL) {
            this._socialPostsService
                .duplicatePosts$({
                    postRefs,
                    postDestination,
                    fromRestaurantId,
                    restaurantIds: [fromRestaurantId],
                })
                .subscribe({
                    next: () => {
                        onDuplicationSuccess();
                    },
                    error: (error) => {
                        onDuplicationError(error);
                    },
                });
            return;
        }

        this._socialPostsService.getPostsToDuplicate$(postRefs).subscribe((result) => {
            const postsToDuplicate = result.data;

            const steps = this._getStepsForDuplication({
                posts: postsToDuplicate,
                postDestination,
                fromRestaurantId,
                skipRestaurantSelection: true,
                isDirectlyAfterUpsertPostModal,
                submitPublicationStatus,
            });

            const initialData: DuplicateSocialPostWithTextGenerationInputData = {
                selectedRestaurants: [this._restaurantsService.currentRestaurant],
                index: 0,
            };

            this._customDialogService.open(StepperModalComponent, {
                width: 'unset',
                height: 'unset',
                panelClass: 'malou-dialog-panel--without-border-radius',
                data: {
                    steps,
                    initialData,
                    fullScreenStepIndexes: steps.map((_, index) => index),
                    sharedData: {
                        postsToDuplicate,
                        postDestination,
                        isDirectlyAfterUpsertPostModal,
                    },
                    title: this._translateService.instant('social_posts.duplicate_here_dialog.seo.title'),

                    onSuccess: (_data: { selectedRestaurants: Restaurant[]; index: number }) => {
                        onDuplicationSuccess();
                    },
                    onError: (error: unknown) => {
                        onDuplicationError(error);
                    },
                    shouldDisplayConfirmationCloseModalAfterClosed: true,
                    malouDialogBodyCustomStyle: {
                        padding: '0px',
                        margin: '0px',
                    },
                },
            });
        });
    }

    private _duplicateToOtherRestaurants({
        postRefs,
        postDestination,
        fromRestaurantId,
        onDuplicationSuccess,
        onDuplicationError,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        postDestination: PostSource;
        fromRestaurantId: string;
        onDuplicationSuccess: () => void;
        onDuplicationError: (error: unknown) => void;
    }): void {
        this._socialPostsService.getPostsToDuplicate$(postRefs).subscribe((result) => {
            const postsToDuplicate = result.data;
            const steps = this._getStepsForDuplication({ posts: postsToDuplicate, postDestination, fromRestaurantId });

            const containsReel = postsToDuplicate.some((post) => post.isReel());
            const containsPost = postsToDuplicate.some((post) => !post.isReel());

            const platformsForSocialPosts = this._getSocialPlatformKeysWithPost();
            const platformsForReels = this._getSocialPlatformKeysWithReel();
            const platformsForSeo = this._getSeoPlatformKeys();

            let hasOneOfThesePlatforms: PlatformKey[] = [];

            if (postDestination === PostSource.SEO) {
                hasOneOfThesePlatforms = platformsForSeo;
            } else {
                if (containsPost && containsReel) {
                    hasOneOfThesePlatforms = intersection(platformsForSocialPosts, platformsForReels);
                } else if (containsPost) {
                    hasOneOfThesePlatforms = platformsForSocialPosts;
                } else if (containsReel) {
                    hasOneOfThesePlatforms = platformsForReels;
                } else {
                    console.error('Posts to duplicate do not contain any post or reel'); // should never happen
                    return;
                }
            }

            const initialData: RestaurantsSelectionData = {
                skipOwnRestaurant: true,
                withoutBrandBusiness: postDestination === PostSource.SEO,
                selectedRestaurants: [],
                hasPlatform: hasOneOfThesePlatforms,
                disableRestaurantWithoutMapstrPremium: false,
            };

            this._customDialogService.open(StepperModalComponent, {
                width: 'unset',
                height: 'unset',
                panelClass: 'malou-dialog-panel--without-border-radius',
                data: {
                    steps,
                    initialData,
                    fullScreenStepIndexes: steps.map((_, index) => index).filter((index) => index !== 0),
                    sharedData: {
                        postsToDuplicate,
                        postDestination,
                    },
                    title: this._translateService.instant('duplicate_to_restaurants_dialog.title'),

                    onSuccess: (_data: { selectedRestaurants: Restaurant[]; index: number }) => {
                        onDuplicationSuccess();
                    },
                    onError: (error: unknown) => {
                        onDuplicationError(error);
                    },
                    shouldDisplayConfirmationCloseModalAfterClosed: true,
                    malouDialogBodyCustomStyle: {
                        padding: '0px',
                        margin: '0px',
                    },
                },
            });
        });
    }

    private _getStepsForDuplication({
        posts,
        postDestination,
        fromRestaurantId,
        skipRestaurantSelection = false,
        isDirectlyAfterUpsertPostModal = false,
        submitPublicationStatus,
    }: {
        posts: PostToDuplicate[];
        postDestination: PostSource;
        fromRestaurantId: string;
        skipRestaurantSelection?: boolean;
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): Step[] {
        const postCount = posts.length;
        return [
            ...(skipRestaurantSelection
                ? []
                : [
                      {
                          component: RestaurantsSelectionComponent,
                          subtitle: this._translateService.instant('duplicate_to_restaurants_dialog.subtitle'),
                          primaryButtonText: this._translateService.instant('common.next'),
                          nextFunction$: (data: RestaurantsSelectionData) =>
                              of({
                                  selectedRestaurants: data.selectedRestaurants ?? [],
                                  index: 0,
                              }),
                      },
                  ]),
            ...posts.map((post, index) => ({
                component: DuplicateSocialPostWithTextGenerationPreviewModalV2Component,
                primaryButtonText:
                    postCount > 1 && index < postCount - 1
                        ? this._translateService.instant('common.duplicate_and_go_next') + ` (${index + 1}/${postCount})`
                        : this._translateService.instant('common.duplicate'),
                hideSecondaryButton: index > 0,
                nextFunction$: (
                    data: DuplicateSocialPostPreviewModalSubmitData[]
                ): Observable<{ selectedRestaurants: Restaurant[]; index: number }> =>
                    this._socialPostsService
                        .duplicatePosts$({
                            fromRestaurantId,
                            restaurantIds: data.map((d) => d.restaurant.id),
                            postRefs:
                                isDirectlyAfterUpsertPostModal && submitPublicationStatus === SubmitPublicationStatus.NOW
                                    ? /**
                                       * During publication, the post is deleted, so passing its ID for the duplication is a bad idea.
                                       * Instead, we pass the `bindingId`, which is shared across versions of the same post.
                                       * This is a special-case workaround with a known drawback: the API may find multiple
                                       * posts with the same `bindingId` and will select one somewhat arbitrarily.
                                       * While not ideal, this is the most reliable option available for this scenario.
                                       */
                                      [{ bindingId: post.bindingId }]
                                    : [{ id: post.id }],
                            postDestination,
                            customFields: data.map((d) => ({
                                restaurantId: d.restaurant.id,
                                hashtags: d.hashtags,
                                location: d.location,
                                text: d.text,
                                plannedPublicationDate: d.plannedPublicationDate,
                                platformKeys: d.platformKeys,
                                published: d.status,
                                callToAction: d.callToAction,
                                offer: d.offer,
                                event: d.event,
                                postTopic: d.postTopic,
                            })),
                        })
                        .pipe(
                            tap(() => {
                                const currentUser = this._store.selectSignal(selectUserInfos);
                                let heapEventName: HeapEventName;
                                if (postDestination === PostSource.SEO) {
                                    heapEventName =
                                        postCount > 1
                                            ? HeapEventName.SOCIAL_POSTS_DUPLICATE_REF_BULK_V2
                                            : HeapEventName.SOCIAL_POSTS_DUPLICATE_REF_V2;
                                } else {
                                    heapEventName =
                                        postCount > 1
                                            ? HeapEventName.SOCIAL_POSTS_DUPLICATE_TO_OTHER_RESTAURANTS_BULK_V2
                                            : HeapEventName.SOCIAL_POSTS_DUPLICATE_TO_OTHER_RESTAURANTS_V2;
                                }

                                this._heapService.track(heapEventName, {
                                    restaurantIds: data.map((d) => d.restaurant.id),
                                    postBindingId: post.bindingId,
                                    userId: currentUser()?.id,
                                    email: currentUser()?.email,
                                });
                                this._toastService.openSuccessToast(this._translateService.instant('social_posts.success_duplicate_post'));
                            }),
                            map(() => ({ selectedRestaurants: data.map((d) => d.restaurant), index: index + 1 })),
                            catchError((error) => {
                                console.error('Error while duplicating post', error);
                                this._toastService.openErrorToast(this._translateService.instant('social_posts.error_duplicate_post'));
                                return of({ selectedRestaurants: data.map((d) => d.restaurant), index: index + 1 });
                            })
                        ),
            })),
        ];
    }

    private _getSocialPlatformKeysWithPost(): PlatformKey[] {
        const allPlatformKeys = getSocialPlatformKeysWithPost();
        return this._getEnabledPlatformKeys(allPlatformKeys);
    }

    private _getSocialPlatformKeysWithReel(): PlatformKey[] {
        const allPlatformKeys = getPlatformKeysWithReel();
        return this._getEnabledPlatformKeys(allPlatformKeys);
    }

    private _getSeoPlatformKeys(): PlatformKey[] {
        const allPlatformKeys = getSeoPlatformKeysWithPost();
        return this._getEnabledPlatformKeys(allPlatformKeys);
    }

    private _getEnabledPlatformKeys(platformKeys: PlatformKey[]): PlatformKey[] {
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();
        const enabledPlatformKeys = platformKeys.filter((platformKey) => {
            const ffPlatform = featureFlaggedPlatforms.find((platform) => platform.key === platformKey);
            return !ffPlatform || (ffPlatform.featureFlagKey && this._experimentationService.isFeatureEnabled(ffPlatform.featureFlagKey));
        });
        return enabledPlatformKeys;
    }
}
