import { SocialPostMediaDto } from '@malou-io/package-dto';
import { AspectRatio, MediaType, RemoveMethodsFromEntity } from '@malou-io/package-utils';

import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';

interface TransformData {
    aspectRatio: AspectRatio;
    rotationInDegrees: number;
    left: number;
    top: number;
    width: number;
    height: number;
}

interface Dimensions {
    width: number;
    height: number;
}

export type ISocialPostMedia = RemoveMethodsFromEntity<SocialPostMedia>;

export class SocialPostMedia {
    url: string;
    type: MediaType;
    thumbnailUrl?: string;
    thumbnailDimensions?: Dimensions;
    transformData?: TransformData;
    duration?: number;
    aspectRatio?: number;

    constructor(data: ISocialPostMedia) {
        this.url = data.url;
        this.type = data.type;
        this.thumbnailUrl = data.thumbnailUrl;
        this.thumbnailDimensions = data.thumbnailDimensions;
        this.transformData = data.transformData;
        this.duration = data.duration;
        this.aspectRatio = data.aspectRatio;
    }

    static fromDto(dto: SocialPostMediaDto): SocialPostMedia {
        return new SocialPostMedia({
            url: dto.url,
            type: dto.type,
            thumbnailUrl: dto.thumbnailUrl,
            thumbnailDimensions: dto.thumbnailDimensions,
            transformData: dto.transformData,
            duration: dto.duration,
            aspectRatio: dto.aspectRatio,
        });
    }

    static fromEditionMedia(media: EditionMedia): SocialPostMedia {
        return new SocialPostMedia({
            url: media.type === MediaType.VIDEO ? media.videoUrl : media.thumbnail1024OutsideUrl,
            type: media.type,
            thumbnailUrl: media.thumbnail1024OutsideUrl,
            thumbnailDimensions: media.thumbnail1024OutsideDimensions,
            transformData: media.transformData,
            duration: media.type === MediaType.VIDEO ? media.duration : undefined,
            aspectRatio: media.aspectRatio,
        });
    }
}
