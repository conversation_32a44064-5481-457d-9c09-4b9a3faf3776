import { Component, inject, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';

import { HeapEventName, PostSource } from '@malou-io/package-utils';

import { DialogService } from ':core/services/dialog.service';
import { HeapService } from ':core/services/heap.service';
import { SocialPostsListV2Component } from ':modules/posts-v2/social-posts/components/social-posts-list/social-posts-list.component';
import { UpsertSocialPostModalComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/upsert-social-post-modal.component';
import {
    UpsertSocialPostModalProps,
    UpsertSocialPostModalResult,
} from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/upsert-social-post-modal.interface';
import { DuplicateSocialPostsService } from ':modules/posts-v2/social-posts/duplicate-social-posts.service';
import { GoToMobileAppComponent } from ':modules/posts-v2/social-posts/social-posts-root/go-to-mobile-app/go-to-mobile-app.component';
import { SocialPostsContext } from ':modules/posts-v2/social-posts/social-posts.context';
import { selectUserInfos } from ':modules/user/store/user.selectors';
import { DialogVariant } from ':shared/components/malou-dialog/malou-dialog.component';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { PluralTranslatePipe } from ':shared/pipes/plural-translate.pipe';
import { CustomDialogService, DialogScreenSize } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-social-posts-root',
    templateUrl: './social-posts-root.component.html',
    styleUrls: ['./social-posts-root.component.scss'],
    imports: [GoToMobileAppComponent, SocialPostsListV2Component],
})
export class SocialPostsRootComponent implements OnDestroy {
    private readonly _activatedRoute = inject(ActivatedRoute);
    private readonly _router = inject(Router);
    private readonly _socialPostsContext = inject(SocialPostsContext);
    private readonly _duplicateSocialPostsService = inject(DuplicateSocialPostsService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _dialogService = inject(DialogService);
    private readonly _translateService = inject(TranslateService);
    private readonly _pluralTranslatePipe = inject(PluralTranslatePipe);
    private readonly _store = inject(Store);
    private readonly _heapService = inject(HeapService);

    ngOnDestroy(): void {
        this._socialPostsContext.stopPollingPostsStatus$.next();
    }

    onCreatePost(): void {
        this._openUpsertSocialPostModal({ disconnectedPlatforms$: this._socialPostsContext.disconnectedPlatforms$ });
    }

    onUpdatePost(data: { postId: string; shouldOpenFeedbacks?: boolean }): void {
        this._openUpsertSocialPostModal({ ...data, disconnectedPlatforms$: this._socialPostsContext.disconnectedPlatforms$ });
    }

    onDeletePost(data: { postId: string; isPublishedOnFacebook: boolean }): void {
        this._dialogService.open({
            title: this._pluralTranslatePipe.transform('social_post.delete_confirmation_modal.title', 1),
            message: data.isPublishedOnFacebook
                ? this._pluralTranslatePipe.transform('social_post.delete_confirmation_modal.message_published_on_facebook', 1)
                : this._pluralTranslatePipe.transform('social_post.delete_confirmation_modal.message', 1),
            variant: DialogVariant.INFO,
            primaryButton: {
                label: this._translateService.instant('common.confirm'),
                action: () => {
                    this._socialPostsContext.deleteSocialPosts([data.postId]);

                    const currentUser = this._store.selectSignal(selectUserInfos);
                    this._heapService.track(HeapEventName.SOCIAL_POSTS_DELETE_V2, {
                        userId: currentUser()?.id,
                        email: currentUser()?.email,
                        restaurantId: this._socialPostsContext.restaurant().id,
                        postId: data.postId,
                    });
                },
            },
            secondaryButton: {
                label: this._translateService.instant('common.cancel'),
            },
        });
    }

    onCreateReelOrTikTok(): void {
        this._openUpsertSocialPostModal({ isReel: true, disconnectedPlatforms$: this._socialPostsContext.disconnectedPlatforms$ });
    }

    onDuplicateHere({
        postRefs,
        postDestination,
        isDirectlyAfterUpsertPostModal,
        submitPublicationStatus,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        postDestination: PostSource;
        isDirectlyAfterUpsertPostModal?: boolean;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): void {
        this._duplicateSocialPostsService.duplicate({
            postRefs,
            postDestination,
            fromRestaurantId: this._socialPostsContext.restaurant().id,
            onDuplicationSuccess: this._onDuplicationSuccess,
            onDuplicationError: this._onDuplicationError,
            isDirectlyAfterUpsertPostModal,
            isHere: true,
            submitPublicationStatus,
        });
    }

    onDuplicateToOtherRestaurants({
        postRefs,
        postDestination,
    }: {
        postRefs: ({ id: string } | { bindingId: string })[];
        postDestination: PostSource;
    }): void {
        this._duplicateSocialPostsService.duplicate({
            postRefs,
            postDestination,
            fromRestaurantId: this._socialPostsContext.restaurant().id,
            onDuplicationSuccess: this._onDuplicationSuccess,
            onDuplicationError: this._onDuplicationError,
            isHere: false,
        });
    }

    private _onDuplicationSuccess = (): void => {
        this._socialPostsContext.postSelection.unselectAll();
        this._socialPostsContext.isSelecting.set(false);
        this._customDialogService.closeAll();
    };

    private _onDuplicationError = (error: unknown): void => {
        console.error(error);
        this._socialPostsContext.postSelection.unselectAll();
        this._socialPostsContext.isSelecting.set(false);
        this._customDialogService.closeAll();
    };

    private _openUpsertSocialPostModal(data: UpsertSocialPostModalProps): void {
        this._customDialogService
            .open<UpsertSocialPostModalComponent, UpsertSocialPostModalProps, UpsertSocialPostModalResult>(
                UpsertSocialPostModalComponent,
                {
                    width: '100vw',
                    disableClose: false,
                    height: '100vh',
                    data,
                },
                { animateScreenSize: DialogScreenSize.ALL }
            )
            .afterClosed()
            .subscribe((result) => {
                if (result?.post) {
                    // Replace the post in the list and in the feed
                    this._socialPostsContext.upsertSocialPost(result.post);

                    // Re-fetch the filter options count
                    this._socialPostsContext.shouldFetchFilterOptionsCount$.next();

                    if (result.duplicateToSeo) {
                        this.onDuplicateHere({
                            postRefs: [{ id: result.post.id }],
                            postDestination: PostSource.SEO,
                            isDirectlyAfterUpsertPostModal: true,
                            submitPublicationStatus: result.submitPublicationStatus,
                        });
                    }
                }

                if (result?.scrollToPostId) {
                    this._socialPostsContext.setFetchUntilPostIsFound(result.scrollToPostId);
                    this._socialPostsContext.fetchPostsUntilPostIsFound(result.scrollToPostId);
                    this._socialPostsContext.highlightPosts([result.scrollToPostId]);
                }

                const queryParams = this._activatedRoute.snapshot.queryParams;
                if (Object.keys(queryParams).length > 0) {
                    // Remove postId query param from the URL
                    this._router.navigate(['.'], { relativeTo: this._activatedRoute, queryParams: {} });
                }
            });
    }
}
