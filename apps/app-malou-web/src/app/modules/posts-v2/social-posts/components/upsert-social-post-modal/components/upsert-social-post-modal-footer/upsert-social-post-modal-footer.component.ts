import { ChangeDetectionStrategy, Component, computed, inject, input, output } from '@angular/core';

import { UpsertSocialPostContext } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/contexts/upsert-social-post.context';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { UpsertGenericPostModalFooterComponent } from ':shared/components/posts-v2/upsert-generic-post-modal-footer/upsert-generic-post-modal-footer.component';

@Component({
    selector: 'app-upsert-social-post-modal-footer',
    templateUrl: './upsert-social-post-modal-footer.component.html',
    styleUrls: ['./upsert-social-post-modal-footer.component.scss'],
    imports: [UpsertGenericPostModalFooterComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UpsertSocialPostModalFooterComponent {
    readonly isDisabled = input.required<boolean>();
    readonly isSubmitting = input.required<boolean>();
    readonly cancel = output<void>();
    readonly savePost = output<SubmitPublicationStatus>();

    private readonly _upsertSocialPostContext = inject(UpsertSocialPostContext);

    readonly selectedDate = this._upsertSocialPostContext.upsertSocialPostState.post.plannedPublicationDate;

    readonly postErrors = this._upsertSocialPostContext.postErrors;
    readonly finalPostErrors = computed((): string[] => (this.selectedOption() === SubmitPublicationStatus.DRAFT ? [] : this.postErrors()));
    readonly disabled = computed((): boolean => this.finalPostErrors().length > 0);

    readonly willDuplicate = computed(() => this._upsertSocialPostContext.upsertSocialPostState.duplicateToPlatforms().length > 0);

    readonly selectedOption = this._upsertSocialPostContext.submitPublicationStatus;

    onSelectedDateChange(date: Date | null): void {
        if (!date) {
            return;
        }
        this._upsertSocialPostContext.updatePlannedPublicationDate(date);
    }
}
