import { BehaviorSubject } from 'rxjs';

import { SocialPostItem } from ':modules/posts-v2/social-posts/models/social-post-item';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { Platform } from ':shared/models';

export interface UpsertSocialPostModalProps {
    postId?: string;
    shouldOpenFeedbacks?: boolean;
    date?: Date;
    isReel?: boolean;
    disconnectedPlatforms$: BehaviorSubject<Platform[]>;
}

export interface UpsertSocialPostModalResult {
    post: SocialPostItem | null;
    scrollToPostId?: string;
    duplicateToSeo: boolean;
    submitPublicationStatus?: SubmitPublicationStatus;
}
