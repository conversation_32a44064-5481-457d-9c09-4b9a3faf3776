import { AsyncPipe, CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, effect, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { catchError, map, Observable, of, take } from 'rxjs';

import { GetStoredInsightsRequestInputBodyDto } from '@malou-io/package-dto';
import { InsightsChart, MalouComparisonPeriod, MalouMetric, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { InsightsService } from ':modules/statistics/insights.service';
import { CommunityV2Component } from ':modules/statistics/social-networks/community-v2/community.component';
import { SocialNetworksPostsInsightsContext } from ':modules/statistics/social-networks/context/social-networks-post-insights.context';
import { EngagementV2Component } from ':modules/statistics/social-networks/engagement-v2/engagement.component';
import { PostsInsightsTableComponent } from ':modules/statistics/social-networks/posts-insights-table/posts-insights-table.component';
import { CommunityChartData } from ':modules/statistics/social-networks/social-networks.interfaces';
import * as StatisticsActions from ':modules/statistics/store/statistics.actions';
import { ChartOptions } from ':shared/components/download-insights-modal/download-insights.interface';
import { ViewBy } from ':shared/enums/view-by.enum';
import { getDayMonthYearFromDate } from ':shared/helpers';
import { parseInsightsRouteParams } from ':shared/helpers/extract-statistics-route-data';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { FromToDateFormatterPipe } from ':shared/pipes/from-to-date-formatter.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';
import { StatisticsPdfRestaurantsFormatterPipe } from ':shared/pipes/statistics-pdf-restaurants-formatter.pipe';

@Component({
    selector: 'app-social-network-pdf',
    imports: [
        CommonModule,
        PostsInsightsTableComponent,
        AsyncPipe,
        TranslateModule,
        FromToDateFormatterPipe,
        IncludesPipe,
        AsyncPipe,
        StatisticsPdfRestaurantsFormatterPipe,
        CommunityV2Component,
        EngagementV2Component,
    ],
    templateUrl: './social-network-pdf.component.html',
    styleUrls: ['./social-network-pdf.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialNetworkPdfComponent {
    readonly InsightsChart = InsightsChart;
    readonly ViewBy = ViewBy;

    private readonly _store = inject(Store);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _insightsService = inject(InsightsService);
    private readonly _socialNetworksPostsInsightsContext = inject(SocialNetworksPostsInsightsContext);
    public readonly translateService = inject(TranslateService);

    chartOptions: ChartOptions = {};
    displayedCharts: InsightsChart[] = [];
    displayedPlatforms: string;

    startDate: Date;
    endDate: Date;

    communityHasData = true;
    engagementHasData = true;

    readonly followers = signal<CommunityChartData>({} as CommunityChartData);
    private readonly _restaurantId = toSignal<string | undefined>(
        this._restaurantsService.restaurantSelected$.pipe(map((restaurant) => restaurant?._id)),
        { initialValue: undefined }
    );

    readonly selectedRestaurantTitle$: Observable<string>;

    constructor() {
        this._socialNetworksPostsInsightsContext.postsWithInsights$.pipe(take(1)).subscribe();

        const parsedQueryParams = parseInsightsRouteParams();
        const { dates, displayedCharts, chartOptions, platformKeys, comparisonPeriod } = parsedQueryParams;

        this.chartOptions = chartOptions ?? {};
        this.displayedCharts = displayedCharts;

        this.startDate = dates.startDate;
        this.endDate = dates.endDate;

        this.displayedPlatforms = this.translateService.instant('statistics_pdf.e_reputation_pdf.platforms', {
            platforms: (platformKeys ?? []).map((platformKey) => this._enumTranslatePipe.transform(platformKey, 'platform_key')).join(', '),
        });

        this._store.dispatch(StatisticsActions.editPlatforms({ page: PlatformFilterPage.SOCIAL_NETWORKS, platforms: platformKeys ?? [] }));

        this._store.dispatch(
            StatisticsActions.editComparisonPeriod({ comparisonPeriod: comparisonPeriod ?? MalouComparisonPeriod.PREVIOUS_PERIOD })
        );

        this.selectedRestaurantTitle$ = this._restaurantsService.restaurantSelected$.pipe(
            map((restaurant) => restaurant?.internalName ?? restaurant?.name ?? '')
        );

        effect(() => {
            const restaurantId = this._restaurantId();
            if (!restaurantId) {
                return;
            }

            const getFollowersInsights$ = this._getFollowersInsights$(restaurantId, platformKeys ?? [], this.startDate, this.endDate);

            getFollowersInsights$.pipe(catchError(() => of({}))).subscribe((followers) => {
                this.followers.set(followers);
            });
        });
    }

    private _getFollowersInsights$(
        restaurantId: string,
        platformKeys: PlatformKey[],
        startDate: Date,
        endDate: Date
    ): Observable<CommunityChartData> {
        const requestBody: GetStoredInsightsRequestInputBodyDto = {
            restaurantIds: [restaurantId],
            platformKeys,
            startDate: getDayMonthYearFromDate(startDate),
            endDate: getDayMonthYearFromDate(endDate),
            metrics: [MalouMetric.FOLLOWERS],
        };

        return this._insightsService.getStoredInsights(requestBody).pipe(
            map((response) => {
                const currentRestaurantInsights = response.data?.[restaurantId];
                return currentRestaurantInsights
                    ? {
                          [PlatformKey.FACEBOOK]: currentRestaurantInsights[PlatformKey.FACEBOOK]?.insights,
                          [PlatformKey.INSTAGRAM]: currentRestaurantInsights[PlatformKey.INSTAGRAM]?.insights,
                          [PlatformKey.TIKTOK]: currentRestaurantInsights[PlatformKey.TIKTOK]?.insights,
                      }
                    : {};
            }),
            catchError(() => of({}))
        );
    }
}
