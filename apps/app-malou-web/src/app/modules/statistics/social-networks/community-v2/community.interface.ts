import { MalouComparisonPeriod } from '@malou-io/package-utils';

import { ChartDataArray } from ':shared/helpers';

export interface CommunityData {
    instagramFollowers: ChartDataArray;
    facebookFollowers: ChartDataArray;
    tiktokFollowers: ChartDataArray;
    totalFollowers: ChartDataArray;
    previousInstagramFollowers: ChartDataArray;
    previousFacebookFollowers: ChartDataArray;
    previousTiktokFollowers: ChartDataArray;
    previousTotalFollowers: ChartDataArray;
    instagramNewFollowers: ChartDataArray;
    facebookNewFollowers: ChartDataArray;
    tiktokNewFollowers: ChartDataArray;
    totalNewFollowers: ChartDataArray;
    comparisonPeriod?: MalouComparisonPeriod;
}

export interface CurrentAndDiffInsights {
    current: number | null;
    diff: number | null;
    previous: number | null;
}
