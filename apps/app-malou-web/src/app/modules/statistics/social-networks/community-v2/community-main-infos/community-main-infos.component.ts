import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input, Signal } from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { first, isNumber, last } from 'lodash';

import { isNotNil, MalouComparisonPeriod } from '@malou-io/package-utils';

import { CommunityData, CurrentAndDiffInsights } from ':modules/statistics/social-networks/community-v2/community.interface';
import { NumberEvolutionComponent } from ':shared/components/number-evolution/number-evolution.component';
import { ChartDataArray } from ':shared/helpers';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';

@Component({
    selector: 'app-community-main-infos-v2',
    templateUrl: './community-main-infos.html',
    styleUrls: ['./community-main-infos.scss'],
    imports: [ShortNumberPipe, NumberEvolutionComponent, TranslateModule, NgTemplateOutlet, MatTooltipModule],
    providers: [],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CommunityMainInfosV2Component {
    readonly communityData = input.required<CommunityData>();

    readonly comparisonPeriodKey = computed(() => {
        const comparisonPeriod = this.communityData().comparisonPeriod ?? MalouComparisonPeriod.PREVIOUS_PERIOD;
        return `date_filter.comparison_period.${comparisonPeriod}`;
    });

    readonly comparedToKey = computed(() => {
        const comparisonPeriod = this.communityData().comparisonPeriod ?? MalouComparisonPeriod.PREVIOUS_PERIOD;
        return `statistics.common.compared_to.${comparisonPeriod}`;
    });

    readonly followersPerPlatform = computed(() => {
        if (
            !this._hasOnePlatform(
                this.communityData().instagramFollowers,
                this.communityData().facebookFollowers,
                this.communityData().tiktokFollowers
            )
        ) {
            const facebookLastFollowersCurrentPeriod = last(this.communityData().facebookFollowers.filter(isNotNil));
            const facebookFirstFollowersCurrentPeriod = first(this.communityData().facebookFollowers.filter(isNotNil));

            const instagramLastFollowersCurrentPeriod = last(this.communityData().instagramFollowers.filter(isNotNil));
            const instagramFirstFollowersCurrentPeriod = first(this.communityData().instagramFollowers.filter(isNotNil));

            const tiktokLastFollowersCurrentPeriod = last(this.communityData().tiktokFollowers.filter(isNotNil));
            const tiktokFirstFollowersCurrentPeriod = first(this.communityData().tiktokFollowers.filter(isNotNil));

            const facebookLastFollowersPreviousPeriod = last(this.communityData().previousFacebookFollowers.filter(isNotNil));
            const facebookFirstFollowersPreviousPeriod = first(this.communityData().previousFacebookFollowers.filter(isNotNil));

            const instagramLastFollowersPreviousPeriod = last(this.communityData().previousInstagramFollowers.filter(isNotNil));
            const instagramFirstFollowersPreviousPeriod = first(this.communityData().previousInstagramFollowers.filter(isNotNil));

            const tiktokLastFollowersPreviousPeriod = last(this.communityData().previousTiktokFollowers.filter(isNotNil));
            const tiktokFirstFollowersPreviousPeriod = first(this.communityData().previousTiktokFollowers.filter(isNotNil));

            return {
                current: {
                    instagram: {
                        lastFollowers: instagramLastFollowersCurrentPeriod ?? null,
                        firstFollowers: instagramFirstFollowersCurrentPeriod ?? null,
                    },
                    facebook: {
                        lastFollowers: facebookLastFollowersCurrentPeriod ?? null,
                        firstFollowers: facebookFirstFollowersCurrentPeriod ?? null,
                    },
                    tiktok: {
                        lastFollowers: tiktokLastFollowersCurrentPeriod ?? null,
                        firstFollowers: tiktokFirstFollowersCurrentPeriod ?? null,
                    },
                },
                previous: {
                    instagram: {
                        lastFollowers: instagramLastFollowersPreviousPeriod ?? null,
                        firstFollowers: instagramFirstFollowersPreviousPeriod ?? null,
                    },
                    facebook: {
                        lastFollowers: facebookLastFollowersPreviousPeriod ?? null,
                        firstFollowers: facebookFirstFollowersPreviousPeriod ?? null,
                    },
                    tiktok: {
                        lastFollowers: tiktokLastFollowersPreviousPeriod ?? null,
                        firstFollowers: tiktokFirstFollowersPreviousPeriod ?? null,
                    },
                },
            };
        }
        return null;
    });

    readonly totalFollowers: Signal<CurrentAndDiffInsights> = computed(() => {
        if (
            this._hasOnePlatform(
                this.communityData().instagramFollowers,
                this.communityData().facebookFollowers,
                this.communityData().tiktokFollowers
            )
        ) {
            const currentTotalFollowers = last(this.communityData().totalFollowers.filter(isNotNil));
            const previousTotalFollowers = last(this.communityData().previousTotalFollowers.filter(isNotNil));
            return {
                current: currentTotalFollowers ?? null,
                diff:
                    isNumber(previousTotalFollowers) && isNumber(currentTotalFollowers)
                        ? currentTotalFollowers - previousTotalFollowers
                        : null,
                previous: previousTotalFollowers ?? null,
            };
        }

        const followersPerPlatform = this.followersPerPlatform();
        if (!followersPerPlatform) {
            return {
                current: null,
                diff: null,
                previous: null,
            };
        }

        const {
            current: {
                instagram: { lastFollowers: instagramLastFollowersCurrentPeriod },
                facebook: { lastFollowers: facebookLastFollowersCurrentPeriod },
                tiktok: { lastFollowers: tiktokLastFollowersCurrentPeriod },
            },
            previous: {
                instagram: { lastFollowers: instagramLastFollowersPreviousPeriod },
                facebook: { lastFollowers: facebookLastFollowersPreviousPeriod },
                tiktok: { lastFollowers: tiktokLastFollowersPreviousPeriod },
            },
        } = followersPerPlatform;

        const currentLastTotalFollowers =
            isNumber(facebookLastFollowersCurrentPeriod) &&
            isNumber(instagramLastFollowersCurrentPeriod) &&
            isNumber(tiktokLastFollowersCurrentPeriod)
                ? facebookLastFollowersCurrentPeriod + instagramLastFollowersCurrentPeriod + tiktokLastFollowersCurrentPeriod
                : null;

        const previousLastTotalFollowers =
            isNumber(facebookLastFollowersPreviousPeriod) &&
            isNumber(instagramLastFollowersPreviousPeriod) &&
            isNumber(tiktokLastFollowersPreviousPeriod)
                ? facebookLastFollowersPreviousPeriod + instagramLastFollowersPreviousPeriod + tiktokLastFollowersPreviousPeriod
                : null;

        const res = {
            current: currentLastTotalFollowers ?? null,
            diff:
                isNumber(currentLastTotalFollowers) && isNumber(previousLastTotalFollowers)
                    ? currentLastTotalFollowers - previousLastTotalFollowers
                    : null,
            previous: previousLastTotalFollowers ?? null,
        };
        return res;
    });

    readonly totalNewFollowers: Signal<CurrentAndDiffInsights> = computed(() => {
        if (
            this._hasOnePlatform(
                this.communityData().instagramFollowers,
                this.communityData().facebookFollowers,
                this.communityData().tiktokFollowers
            )
        ) {
            const existingValuesTotalFollowers = this.communityData().totalFollowers.filter(isNotNil);
            const firstFollowersValue = first(existingValuesTotalFollowers);
            const lastFollowersValue = last(existingValuesTotalFollowers);
            const currentTotalNewFollowers =
                isNumber(lastFollowersValue) && isNumber(firstFollowersValue) ? lastFollowersValue - firstFollowersValue : null;

            const existingValuesPreviousTotalFollowers = this.communityData().previousTotalFollowers.filter(isNotNil);
            const previousFirstFollowersValue = first(existingValuesPreviousTotalFollowers);
            const previousLastFollowersValue = last(existingValuesPreviousTotalFollowers);
            const previousTotalNewFollowers =
                isNumber(previousLastFollowersValue) && isNumber(previousFirstFollowersValue)
                    ? previousLastFollowersValue - previousFirstFollowersValue
                    : null;
            return {
                current: currentTotalNewFollowers ?? null,
                diff:
                    isNumber(previousTotalNewFollowers) && isNumber(currentTotalNewFollowers)
                        ? currentTotalNewFollowers - previousTotalNewFollowers
                        : null,
                previous: previousTotalNewFollowers ?? null,
            };
        }

        const followersPerPlatform = this.followersPerPlatform();
        if (!followersPerPlatform) {
            return {
                current: null,
                diff: null,
                previous: null,
            };
        }

        const {
            current: {
                instagram: { firstFollowers: instagramFirstFollowersCurrentPeriod, lastFollowers: instagramLastFollowersCurrentPeriod },
                facebook: { firstFollowers: facebookFirstFollowersCurrentPeriod, lastFollowers: facebookLastFollowersCurrentPeriod },
                tiktok: { firstFollowers: tiktokFirstFollowersCurrentPeriod, lastFollowers: tiktokLastFollowersCurrentPeriod },
            },
            previous: {
                instagram: { firstFollowers: instagramFirstFollowersPreviousPeriod, lastFollowers: instagramLastFollowersPreviousPeriod },
                facebook: { firstFollowers: facebookFirstFollowersPreviousPeriod, lastFollowers: facebookLastFollowersPreviousPeriod },
                tiktok: { firstFollowers: tiktokFirstFollowersPreviousPeriod, lastFollowers: tiktokLastFollowersPreviousPeriod },
            },
        } = followersPerPlatform;

        const currentLastTotalFollowers =
            isNumber(facebookLastFollowersCurrentPeriod) &&
            isNumber(instagramLastFollowersCurrentPeriod) &&
            isNumber(tiktokLastFollowersCurrentPeriod)
                ? facebookLastFollowersCurrentPeriod + instagramLastFollowersCurrentPeriod + tiktokLastFollowersCurrentPeriod
                : null;

        const currentFirstTotalFollowers =
            isNumber(facebookFirstFollowersCurrentPeriod) &&
            isNumber(instagramFirstFollowersCurrentPeriod) &&
            isNumber(tiktokFirstFollowersCurrentPeriod)
                ? facebookFirstFollowersCurrentPeriod + instagramFirstFollowersCurrentPeriod + tiktokFirstFollowersCurrentPeriod
                : null;

        const currentPeriodFollowersEvolution =
            isNumber(currentLastTotalFollowers) && isNumber(currentFirstTotalFollowers)
                ? currentLastTotalFollowers - currentFirstTotalFollowers
                : null;

        const previousLastTotalFollowers =
            isNumber(facebookLastFollowersPreviousPeriod) &&
            isNumber(instagramLastFollowersPreviousPeriod) &&
            isNumber(tiktokLastFollowersPreviousPeriod)
                ? facebookLastFollowersPreviousPeriod + instagramLastFollowersPreviousPeriod + tiktokLastFollowersPreviousPeriod
                : null;

        const previousFirstTotalFollowers =
            isNumber(facebookFirstFollowersPreviousPeriod) &&
            isNumber(instagramFirstFollowersPreviousPeriod) &&
            isNumber(tiktokFirstFollowersPreviousPeriod)
                ? facebookFirstFollowersPreviousPeriod + instagramFirstFollowersPreviousPeriod + tiktokFirstFollowersPreviousPeriod
                : null;

        const previousPeriodFollowersEvolution =
            isNumber(previousLastTotalFollowers) && isNumber(previousFirstTotalFollowers)
                ? previousLastTotalFollowers - previousFirstTotalFollowers
                : null;

        if (!isNumber(previousPeriodFollowersEvolution)) {
            return {
                current: currentPeriodFollowersEvolution ?? null,
                diff: currentPeriodFollowersEvolution ?? null,
                previous: previousLastTotalFollowers ?? null,
            };
        }
        if (!isNumber(currentPeriodFollowersEvolution)) {
            return {
                current: currentPeriodFollowersEvolution ?? null,
                diff: -previousPeriodFollowersEvolution,
                previous: previousLastTotalFollowers ?? null,
            };
        }
        return {
            current: currentPeriodFollowersEvolution,
            diff: currentPeriodFollowersEvolution - previousPeriodFollowersEvolution,
            previous: previousPeriodFollowersEvolution,
        };
    });

    private _hasOnePlatform(
        instagramFollowers: ChartDataArray,
        facebookFollowers: ChartDataArray,
        tiktokFollowers: ChartDataArray
    ): boolean {
        return instagramFollowers.length === 0 || facebookFollowers.length === 0 || tiktokFollowers.length === 0;
    }
}
