import { Chart } from 'chart.js';
import { DateTime } from 'luxon';

import { PlatformKey } from '@malou-io/package-utils';

import { ViewBy } from ':shared/enums/view-by.enum';

export const malouChartColorABC = '#040707';
export const malouChartColorDeliveroo = '#20CFBE';
export const malouChartColorFacebook = '#1977F3';
export const malouChartColorFoursquare = '#F94877';
export const malouChartColorGMB = '#EA4335';
export const malouChartColorInstagram = '#D82E77';
export const malouChartColorLaFourchette = '#006659';
export const malouChartColorMapstr = '#FEC22B';
export const malouChartColorOpentable = '#DA3743';
export const malouChartColorPagesjaunes = '#F4E422';
export const malouChartColorResy = '#FF2910';
export const malouChartColorSevenRooms = '##1F1D1C';
export const malouChartColorTikTok = '#3C3C3C';
export const malouChartColorTripadvisor = '#01E2A0';
export const malouChartColorUberEats = '#0BC26E';
export const malouChartColorYelp = '#D22325';
export const malouChartColorZenchef = '#F2E8E3';
export const malouChartColorDoordash = '#d73721';

export const malouChartColorABC30Percent = 'rgba(4, 7, 7, 0.3)';
export const malouChartColorDeliveroo30Percent = 'rgba(32, 207, 190, 0.3)';
export const malouChartColorFacebook30Percent = 'rgba(25, 119, 243, 0.3)';
export const malouChartColorFoursquare30Percent = 'rgba(249, 72, 119, 0.3)';
export const malouChartColorGMB30Percent = 'rgba(234, 67, 53, 0.3)';
export const malouChartColorInstagram30Percent = 'rgba(216, 46, 119, 0.3)';
export const malouChartColorLaFourchette30Percent = 'rgba(0, 102, 89, 0.3)';
export const malouChartColorMapstr30Percent = 'rgba(254, 194, 43, 0.3)';
export const malouChartColorOpentable30Percent = 'rgba(218, 55, 67, 0.3)';
export const malouChartColorPagesjaunes30Percent = 'rgba(244, 228, 34, 0.3)';
export const malouChartColorResy30Percent = 'rgba(255, 41, 16, 0.3)';
export const malouChartColorSevenRooms30Percent = 'rgba(31, 29, 28, 0.3)';
export const malouChartColorTikTok30Percent = 'rgba(60, 60, 60, 0.3)';
export const malouChartColorTripadvisor30Percent = 'rgba(1, 226, 160, 0.3)';
export const malouChartColorUberEats30Percent = 'rgba(11, 194, 110, 0.3)';
export const malouChartColorYelp30Percent = 'rgba(210, 35, 37, 0.3)';
export const malouChartColorZenchef30Percent = 'rgba(242, 232, 227, 0.3)';
export const malouChartColorDoordash30Percent = 'rgba(215, 55, 33, 0.3)';

export const malouChartColorPink = '#EE116E';
export const malouChartColorPurple = '#AC32B7';
export const malouChartColorBluePurple = '#6A52FD';
export const malouChartColorLightBluePurple = '#9966ff4d';
export const malouChartColorLightPurple = '#C4B4FE';
export const malouChartColorLightPink = '#F788B7';
export const malouChartColorLighterPink = '#FFBBC7';
export const malouChartColorLighterBlue = '#F2F2FF';
export const malouChartColorGreen = '#9AEABA';
export const malouChartColorRed = '#FFBBC7';
export const malouChartColorBlue = '#3F34C4';
export const malouChartColorLightBlue = '#544ace';
export const malouChartColorPurpleDark = '#5926AC';
export const malouChartColorPurpleMedium = '#9171ED';
export const malouChartColorPurplePink = '#C784E7';
export const malouChartColorPinkBarbie = '#EB6CB5';
export const malouChartColorPinkMedium = '#FF8FBE';
export const malouChartColorDarkGreen = '#34B467';

export const malouChartColorPrimary5Percent = 'rgba(106, 82, 253, 0.05)';
export const malouChartColorRed5Percent = 'rgba(238, 17, 110, 0.15)';

export const malouChartColorPurpleLight30Percent = 'rgba(196, 180, 254, 0.3)';
export const malouChartColorPinkLight30Percent = 'rgba(247, 136, 183, 0.3)';
export const malouChartColorBluePurpleLight30Percent = 'rgba(106, 82, 253, 0.3)';
export const malouChartColorBlue30Percent = 'rgba(63, 52, 196, 0.3)';
export const malouChartColorDarkGreen30Percent = 'rgba(52, 180, 103, 0.3)';
export const malouChartColorLighterPink30Percent = 'rgba(255, 187, 199, 0.3)';
export const malouChartColorLighterBlue30Percent = 'rgba(242, 242, 255, 0.3)';

export const malouChartColorPurple50Percent = 'rgba(172, 50, 183, 0.5)';

export const malouChartColorPinkLight60Percent = 'rgba(247, 136, 183, 0.6)';
export const malouChartColorPurpleLight60Percent = 'rgba(196, 180, 254, 0.6)';
export const malouChartColorBluePurpleLight60Percent = 'rgba(106, 82, 253, 0.6)';
export const malouChartColorBlue60Percent = 'rgba(63, 52, 196, 0.6)';
export const malouChartColorDarkGreen60Percent = 'rgba(52, 180, 103, 0.6)';
export const malouChartColorLighterPink60Percent = 'rgba(255, 187, 199, 0.6)';

export const malouChartBackgroundColorRed = '#FFEBEE';
export const malouChartBackgroundColorGreen = '#E1FAEA';

export const malouChartColorText1 = '#0A2540';
export const malouChartColorText2 = '#4A5E73';

export const DEFAULT_POINT_RADIUS = 5;
export const DEFAULT_DASHED_LINE_OPTIONS = [6, 7];

export type ChartDataElement = number | null;
export type ChartDataArray = ChartDataElement[];

/**
 * Compute points radius useful for a line chart.
 * For each data element
 * Return DEFAULT_POINT_RADIUS if the current point is after or before a null element
 * Return 0 otherwise
 * Special case for first and last point
 */
export function computePointsRadius(data: (number | null)[]): number[] {
    if (data.length === 1) {
        return [DEFAULT_POINT_RADIUS];
    }
    return data.map((e, index) => {
        const isFirstIndex = index === 0;
        const isLastIndex = index === data.length - 1;
        const isSecondValueNull = data[1] === null;
        const isBeforeLastValueNull = data[data.length - 2] === null;

        if (isFirstIndex) {
            if (isSecondValueNull) {
                return DEFAULT_POINT_RADIUS;
            } else {
                return 0;
            }
        }
        if (isLastIndex) {
            if (isBeforeLastValueNull) {
                return DEFAULT_POINT_RADIUS;
            } else {
                return 0;
            }
        }

        return data[index - 1] === null || data[index + 1] === null ? DEFAULT_POINT_RADIUS : 0;
    });
}

/**
 * Merge 2 array by adding value at same index
 * If 2 value at the same index are null, the resulting value will be null
 * If at least one value at the same index is not null, the other value is considered equal to 0
 */
function mergeTwoArrays(arr1: ChartDataArray, arr2: ChartDataArray): ChartDataArray {
    if (!arr1.length) {
        return arr2;
    } else if (!arr2.length) {
        return arr1;
    }
    return (arr1 ?? []).map((val1, index) => {
        const val2 = arr2?.[index];
        if (val1 === null && val2 === null) {
            return null;
        }
        return (val1 ?? 0) + (val2 ?? 0);
    });
}

export function mergeArrays(...arrays: ChartDataArray[]): ChartDataArray {
    if (arrays.length === 0) {
        return [];
    }
    return arrays.reduce<ChartDataArray>((acc, curr) => mergeTwoArrays(acc, curr), []);
}

export function toggleVisibilityOnMultipleDatasets(chart: Chart, datasetIndexes: number[], visible: boolean): void {
    const mode = visible ? 'show' : 'hide';
    const metas = datasetIndexes.map((datasetIndex) => chart.getDatasetMeta(datasetIndex));
    const anims = metas.map((meta) => meta.controller['_resolveAnimations'](undefined, mode));
    datasetIndexes.map((datasetIndex) => chart.setDatasetVisibility(datasetIndex, visible));
    anims.map((anim, index) => anim.update(metas[index], { visible }));

    chart.update((ctx) => (datasetIndexes.includes(ctx.datasetIndex) ? mode : 'none'));
}

export function getChartViewByFromDates(startDate: Date | null, endDate: Date | null): ViewBy {
    if (!startDate || !endDate) {
        return ViewBy.DAY;
    }
    const diffInDays = DateTime.fromJSDate(endDate).diff(DateTime.fromJSDate(startDate), 'days').days;
    const diffInMonths = DateTime.fromJSDate(endDate).diff(DateTime.fromJSDate(startDate), 'months').months;

    // last 7 days take the current day into account
    if (diffInDays <= 8) {
        return ViewBy.DAY;
    } else if (diffInMonths < 3) {
        return ViewBy.WEEK;
    } else {
        return ViewBy.MONTH;
    }
}

export function mapPlatformKeyToChartColor(platformKey: PlatformKey): string {
    switch (platformKey) {
        case PlatformKey.ABC:
            return malouChartColorABC;
        case PlatformKey.DELIVEROO:
            return malouChartColorDeliveroo;
        case PlatformKey.FACEBOOK:
            return malouChartColorFacebook;
        case PlatformKey.FOURSQUARE:
            return malouChartColorFoursquare;
        case PlatformKey.GMB:
            return malouChartColorGMB;
        case PlatformKey.INSTAGRAM:
            return malouChartColorInstagram;
        case PlatformKey.LAFOURCHETTE:
            return malouChartColorLaFourchette;
        case PlatformKey.MAPSTR:
            return malouChartColorMapstr;
        case PlatformKey.OPENTABLE:
            return malouChartColorOpentable;
        case PlatformKey.RESY:
            return malouChartColorResy;
        case PlatformKey.TIKTOK:
            return malouChartColorTikTok;
        case PlatformKey.TRIPADVISOR:
            return malouChartColorTripadvisor;
        case PlatformKey.UBEREATS:
            return malouChartColorUberEats;
        case PlatformKey.YELP:
            return malouChartColorYelp;
        case PlatformKey.ZENCHEF:
            return malouChartColorZenchef;
        case PlatformKey.DOORDASH:
            return malouChartColorDoordash;
        default:
            return malouChartColorPurple;
    }
}
