import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { DateTime } from 'luxon';
import { forkJoin, Observable, take } from 'rxjs';

import { PostType } from '@malou-io/package-utils';

import { selectFollowersData, selectPostsWithInsightsData } from ':modules/statistics/store/statistics.selectors';
import { getClosestValueFromDate } from ':shared/helpers';
import { InsightsByPlatform, PostsWithInsightsByPlatforms, PostWithInsights, TimeScaleToMetricToDataValues } from ':shared/models';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { AbstractCsvService, CsvAsStringArrays } from ':shared/services/csv-services/csv-service.abstract';

interface Data {
    postsWithInsights: PostsWithInsightsByPlatforms;
    followersInsights: InsightsByPlatform;
}

@Injectable({ providedIn: 'root' })
export class PublicationsCsvInsightsService extends AbstractCsvService<Data> {
    constructor(
        private readonly _store: Store,
        private readonly _enumTranslatePipe: EnumTranslatePipe,
        private readonly _shortNumberPipe: ShortNumberPipe
    ) {
        super();
    }

    protected override _getData$(): Observable<Data> {
        return forkJoin({
            postsWithInsights: this._store.select(selectPostsWithInsightsData).pipe(take(1)),
            followersInsights: this._store.select(selectFollowersData).pipe(take(1)),
        });
    }
    protected override _getCsvHeaderRow(): CsvAsStringArrays[0] {
        return ['Date', 'Type', 'Caption', 'Platform', 'Impressions', 'Likes', 'Comments', 'Shares', 'Saves', 'Engagement Rate'];
    }
    protected override _getCsvDataRows({ postsWithInsights, followersInsights }: Data): CsvAsStringArrays {
        return postsWithInsights
            .map((platformPostsWithInsights) => {
                const platformKey = Object.keys(platformPostsWithInsights)[0];
                const posts = platformPostsWithInsights[platformKey]?.data;
                if (!posts?.length) {
                    return [];
                }
                return posts.map((post) => {
                    const postWithInsights = new PostWithInsights({
                        ...post,
                        key: platformKey,
                        nbFollowers: this._getFollowerCountByDate(new Date(post.createdAt), followersInsights[platformKey]),
                    });

                    const postDate = new Date(postWithInsights.createdAt).toLocaleDateString();
                    const type = postWithInsights.postType === PostType.REEL ? 'Reel' : 'Post';
                    const caption = postWithInsights.caption ?? '';
                    const platform = this._enumTranslatePipe.transform(platformKey, 'platform_key');
                    const impressions = postWithInsights.impressions?.toString() ?? '0';
                    const likes = postWithInsights.likes?.toString() ?? '0';
                    const comments = postWithInsights.comments?.toString() ?? '0';
                    const shares = postWithInsights.shares?.toString() ?? '0';
                    const saves = postWithInsights.saved?.toString() ?? '0';
                    const engagementRate =
                        typeof postWithInsights.engagementRate === 'number'
                            ? this._shortNumberPipe.transform(postWithInsights.engagementRate)
                            : '';

                    return [postDate, type, caption, platform, impressions, likes, comments, shares, saves, engagementRate];
                });
            })
            .flat();
    }

    private _getFollowerCountByDate(date: Date, platformFollowersInsights: TimeScaleToMetricToDataValues): number | null {
        const formattedTargetDate = DateTime.fromJSDate(date).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).toJSDate();
        const followers = platformFollowersInsights?.by_day?.followers;
        if (!followers) {
            return null;
        }
        const followersByDay = followers.reduce((acc, f) => ({ ...acc, [f.date]: f.value }), {});
        const possibleDates = Object.keys(followersByDay).map((d) => new Date(d));
        const closestDate = getClosestValueFromDate(formattedTargetDate, possibleDates);
        return closestDate ? followersByDay[closestDate.toISOString()] : null;
    }
}
