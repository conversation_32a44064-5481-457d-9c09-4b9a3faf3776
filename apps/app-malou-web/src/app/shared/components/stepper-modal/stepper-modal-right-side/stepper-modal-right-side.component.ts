import { Component, effect, input, viewChild } from '@angular/core';

import { DynamicComponentDirective } from ':shared/directives/dynamic-component.directive';

@Component({
    selector: 'app-stepper-modal-right-side',
    templateUrl: './stepper-modal-right-side.component.html',
    styleUrls: ['./stepper-modal-right-side.component.scss'],
    imports: [DynamicComponentDirective],
    standalone: true,
})
export class StepperModalRightSideComponent<T extends DefaultRightSideComponent, U extends DefaultRightSideComponentInputs<T>> {
    readonly component = input.required<T>();
    readonly componentInputs = input.required<U>();

    readonly dynamicComponent = viewChild(DynamicComponentDirective);

    constructor() {
        effect(() => {
            const dynamicComponent = this.dynamicComponent();
            const component = this.component();
            if (!dynamicComponent || !component) {
                return;
            }
            this._loadComponent(dynamicComponent, component);
        });
    }

    private _loadComponent(dynamicComponent: DynamicComponentDirective, component: T): void {
        const viewContainerRef = dynamicComponent.viewContainerRef;
        viewContainerRef.clear();
        const componentRef = viewContainerRef.createComponent(component);
        Object.entries(this.componentInputs).forEach(([key, value]) => {
            componentRef.setInput(key, value);
        });
    }
}
