@use '_malou_variables.scss' as *;
@use '_malou_functions.scss' as *;
@use '_malou_tooltips.scss' as *;

.wrapper:hover {
    .post-errors-tooltip {
        display: block;
    }
}

$tooltip-width: 250px;

.post-errors-tooltip {
    @extend .mdc-tooltip;
    background-color: $malou-color-text-2 !important;
    width: $tooltip-width;
    position: absolute;
    border-radius: 5px;
    top: 0;
    left: 0;
    transform: translate(-15%, -110%);
    padding: toRem(6px);
    z-index: 9999;
    transition: opacity 0.15s ease-in-out;
    display: none;
}
