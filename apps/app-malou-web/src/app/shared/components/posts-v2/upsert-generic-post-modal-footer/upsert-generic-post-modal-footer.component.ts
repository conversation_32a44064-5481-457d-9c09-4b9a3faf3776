import { ChangeDetectionStrategy, Component, computed, inject, input, model, output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { PostDatePickerComponent } from ':modules/posts-v2/post-date-picker/post-date-picker.component';
import { PostDatePickerSize } from ':modules/posts-v2/post-date-picker/post-date-picker.interface';
import { DropdownButtonComponent } from ':shared/components/dropdown-button/dropdown-button.component';
import { MenuButtonSize } from ':shared/components/menu-button-v2/menu-button-v2.interface';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';

@Component({
    selector: 'app-upsert-generic-post-modal-footer',
    templateUrl: './upsert-generic-post-modal-footer.component.html',
    styleUrls: ['./upsert-generic-post-modal-footer.component.scss'],
    imports: [MatButtonModule, MatMenuModule, MatTooltipModule, TranslateModule, DropdownButtonComponent, PostDatePickerComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UpsertGenericPostModalFooterComponent {
    readonly isDisabled = input.required<boolean>();
    readonly isSubmitting = input.required<boolean>();
    readonly postErrors = input.required<string[]>();
    readonly willDuplicate = input.required<boolean>();
    readonly selectedDate = model.required<Date | null>();
    readonly selectedOption = model.required<SubmitPublicationStatus>();

    readonly cancel = output<void>();
    readonly savePost = output<SubmitPublicationStatus>();

    private readonly _translateService = inject(TranslateService);

    readonly MenuButtonSize = MenuButtonSize;
    readonly PostDatePickerSize = PostDatePickerSize;

    readonly finalPostErrors = computed((): string[] => (this.selectedOption() === SubmitPublicationStatus.DRAFT ? [] : this.postErrors()));
    readonly disabled = computed((): boolean => this.finalPostErrors().length > 0);

    readonly displayPublicationStatusOptionFn = computed(() =>
        this.willDuplicate() ? this.displayPublicationStatusOptionAndDuplicate : this.displayPublicationStatusOption
    );

    readonly DROPDOWN_OPTIONS: SubmitPublicationStatus[] = [
        SubmitPublicationStatus.NOW,
        SubmitPublicationStatus.SCHEDULE,
        SubmitPublicationStatus.DRAFT,
    ];

    readonly SubmitPublicationStatus = SubmitPublicationStatus;

    onCancel(): void {
        this.cancel.emit();
    }

    onDropdownButtonClick(submitPublicationStatus: SubmitPublicationStatus | null): void {
        if (this.disabled()) {
            return;
        }
        if (submitPublicationStatus) {
            this.savePost.emit(submitPublicationStatus);
        }
    }

    onSelectedDateChange(date: Date | null): void {
        if (!date) {
            return;
        }
        this.selectedDate.set(date);
    }

    displayPublicationStatusOption = (status: SubmitPublicationStatus): string => {
        switch (status) {
            case SubmitPublicationStatus.NOW:
                return this._translateService.instant('social_posts.new_social_post.now');
            case SubmitPublicationStatus.SCHEDULE:
                return this._translateService.instant('social_posts.new_social_post.later');
            case SubmitPublicationStatus.DRAFT:
                return this._translateService.instant('social_posts.new_social_post.draft');
            default:
                return '';
        }
    };

    displayPublicationStatusOptionAndDuplicate = (status: SubmitPublicationStatus): string => {
        switch (status) {
            case SubmitPublicationStatus.NOW:
                return this._translateService.instant('social_posts.new_social_post.now_and_duplicate');
            case SubmitPublicationStatus.SCHEDULE:
                return this._translateService.instant('social_posts.new_social_post.later_and_duplicate');
            case SubmitPublicationStatus.DRAFT:
                return this._translateService.instant('social_posts.new_social_post.draft_and_duplicate');
            default:
                return '';
        }
    };
}
