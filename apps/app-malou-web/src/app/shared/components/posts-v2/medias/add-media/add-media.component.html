<div class="flex h-[75px] items-center gap-x-5 rounded-md bg-white p-4">
    <mat-icon class="!h-14 !w-14 !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
    <div class="flex flex-col gap-y-1">
        <div class="malou-text-11--semibold text-malou-color-text-1">
            <span
                class="cursor-pointer text-malou-color-primary"
                [attr.data-testid]="'add-first-media-menu-btn'"
                [matMenuTriggerFor]="uploadMenu"
                >{{ 'social_post_medias.add_media' | translate }}</span
            >
            {{ 'social_post_medias.or_drag_and_drop' | translate }}
        </div>
        <div class="malou-text-10--regular italic text-malou-color-text-2">
            @if (onlyVideo()) {
                {{ 'social_post_medias.video_file' | translate }}
            } @else {
                {{ 'social_post_medias.photo_or_video_file' | translate }}
            }
        </div>
    </div>
</div>

<mat-menu class="malou-mat-menu malou-box-shadow rounded-md" #uploadMenu="matMenu">
    <button mat-menu-item [disabled]="isReadonly()" (click)="fileInput()?.click()">
        <mat-icon class="!h-[16px] !w-[16px]" color="primary" [svgIcon]="SvgIcon.ADD"></mat-icon>
        <span class="malou-text-12--regular text-malou-color-text-2">
            {{ 'common.upload_from_computer' | translate }}
        </span>
    </button>
    <button mat-menu-item [disabled]="isReadonly()" [attr.data-testid]="'add-media-from-computer-btn'" (click)="onImportMediaFromGallery()">
        <mat-icon class="!h-[16px] !w-[16px]" color="primary" [svgIcon]="SvgIcon.IMAGE"></mat-icon>
        <span class="malou-text-12--regular text-malou-color-text-2">{{ 'common.upload_from_gallery' | translate }}</span>
    </button>
</mat-menu>
