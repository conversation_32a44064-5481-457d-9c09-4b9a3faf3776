import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';

import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-add-media',
    templateUrl: './add-media.component.html',
    styleUrls: ['./add-media.component.scss'],
    imports: [MatIconModule, MatMenuModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AddMediaComponent {
    readonly isReadonly = input<boolean>(false);
    readonly fileInput = input<HTMLInputElement | null>(null);
    readonly onlyVideo = input<boolean>(false);

    readonly importMediaFromGallery = output<void>();

    readonly SvgIcon = SvgIcon;

    onImportMediaFromGallery(): void {
        this.importMediaFromGallery.emit();
    }
}
