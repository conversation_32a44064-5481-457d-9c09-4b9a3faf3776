import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { PostPublicationStatus } from '@malou-io/package-utils';

import { PostToDuplicate } from ':modules/posts-v2/social-posts/models/post-to-duplicate';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-social-to-seo-original-duplicated-post',
    imports: [
        TranslateModule,
        PlatformLogoComponent,
        ApplySelfPurePipe,
        LazyLoadImageModule,
        ImagePathResolverPipe,
        DatePipe,
        MatIconModule,
    ],
    templateUrl: './social-to-seo-original-duplicated-post.component.html',
    styleUrl: './social-to-seo-original-duplicated-post.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialToSeoOriginalDuplicatedPostComponent {
    readonly originalPost = input.required<PostToDuplicate>();
    readonly isFromUpsertPostModal = input<boolean>(false);

    private readonly _translateService = inject(TranslateService);

    readonly SvgIcon = SvgIcon;

    readonly postStatus = computed<SubmitPublicationStatus>(() => {
        if (this.originalPost().published === PostPublicationStatus.PUBLISHED) {
            return SubmitPublicationStatus.NOW;
        }
        if (this.originalPost().plannedPublicationDate && this.originalPost().published === PostPublicationStatus.PENDING) {
            return SubmitPublicationStatus.SCHEDULE;
        }
        return SubmitPublicationStatus.DRAFT;
    });

    readonly displayedShortPostStatus = computed(() => {
        switch (this.postStatus()) {
            case SubmitPublicationStatus.NOW:
                return this.isFromUpsertPostModal()
                    ? this._translateService.instant('duplicate_here_dialog.seo.original_post_status.publishing')
                    : this._translateService.instant('duplicate_here_dialog.seo.original_post_status.published');
            case SubmitPublicationStatus.SCHEDULE:
                return this._translateService.instant('duplicate_here_dialog.seo.original_post_status.scheduled');
            case SubmitPublicationStatus.DRAFT:
            default:
                return this._translateService.instant('duplicate_here_dialog.seo.original_post_status.saved_as_draft');
        }
    });

    readonly displayedLongPostStatus = computed(() => {
        switch (this.postStatus()) {
            case SubmitPublicationStatus.NOW:
                return this._translateService.instant('duplicate_here_dialog.seo.original_post_status.publishing_long');
            case SubmitPublicationStatus.SCHEDULE:
                return this._translateService.instant('duplicate_here_dialog.seo.original_post_status.scheduled_long');
            case SubmitPublicationStatus.DRAFT:
            default:
                return this._translateService.instant('duplicate_here_dialog.seo.original_post_status.saved_as_draft_long');
        }
    });
}
