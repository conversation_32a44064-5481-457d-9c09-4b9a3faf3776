import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, input, signal } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectChange, MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { ApplicationLanguage, PlatformKey } from '@malou-io/package-utils';

import { times } from ':core/constants';
import { PostDateStatus } from ':modules/posts/new-post-modal/types';
import { DuplicateStoryForm } from ':shared/components/duplicate-post-preview-modal/duplicate-story-preview-modal/duplicate-story-preview-modal.component';
import { InputDatePickerComponent } from ':shared/components/input-date-picker/input-date-picker.component';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { InputTextTheme } from ':shared/components/input-text/input-text.interface';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { isPastHour } from ':shared/helpers';
import { Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplyPurePipe, ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { FormatTimePipe } from ':shared/pipes/format-time.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

@Component({
    selector: 'app-story-preview-card',
    imports: [
        NgClass,
        NgTemplateOutlet,
        MatIconModule,
        MatSelectModule,
        MatTooltipModule,
        MatFormFieldModule,
        FormsModule,
        ReactiveFormsModule,
        TranslateModule,
        InputDatePickerComponent,
        InputTextComponent,
        SelectComponent,
        PlatformLogoComponent,
        ApplySelfPurePipe,
        ImagePathResolverPipe,
        ApplyPurePipe,
        FormatTimePipe,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    templateUrl: './story-preview-card.component.html',
    styleUrl: './story-preview-card.component.scss',
})
export class StoryPreviewCardComponent {
    readonly shouldDisplayDateForm = input.required<boolean>();
    readonly postForm = input.required<FormGroup<DuplicateStoryForm>>();
    readonly shouldDisplayColumnHeader = input.required<boolean>();

    private readonly _translateService = inject(TranslateService);

    readonly currentLang = signal<ApplicationLanguage>(this._translateService.currentLang as ApplicationLanguage);
    readonly postDateStatuses = Object.values(PostDateStatus);

    readonly TIMES = times;
    readonly MIN_DATE = new Date();
    readonly SvgIcon = SvgIcon;
    readonly InputTextTheme = InputTextTheme;

    isPastHour = isPastHour;

    get restaurant(): Restaurant {
        return this.postForm().get('restaurant')!.value;
    }

    get keys(): PlatformKey[] {
        return this.postForm().get('keys')!.value;
    }

    get hasPlatformsConnected(): boolean {
        return this.postForm().get('hasPlatformsConnected')!.value;
    }

    changeSelectedTime(event: MatSelectChange): void {
        this.postForm().patchValue({
            postTime: event.value,
        });
    }

    statusDisplayWith = (status: PostDateStatus): string => this._translateService.instant('posts.duplication_modal.status.' + status);
}
