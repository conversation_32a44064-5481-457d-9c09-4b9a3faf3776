<div class="flex flex-col rounded-[10px] bg-malou-color-background-dark">
    <div class="flex items-center justify-between">
        <span class="malou-text-13--bold m-4 text-malou-color-text-1">
            {{
                storyIndex() !== undefined && totalPostCount() !== undefined && totalPostCount()! > 1
                    ? ('posts.duplicate_post_modal.original_post_with_count'
                      | translate: { index: storyIndex()! + 1, total: totalPostCount() })
                    : ('posts.duplicate_post_modal.original_post' | translate)
            }}
        </span>

        <div class="mr-4 flex gap-1">
            @for (platformKey of story().platformKeys; track platformKey; let index = $index; let first = $first) {
                <app-platform-logo imgClasses="h-5 w-5 max-w-fit" [logo]="platformKey"></app-platform-logo>
            }
        </div>
    </div>

    <div class="flex gap-4 border-y-[1px] border-white px-4 pb-5 pt-4">
        <div class="flex w-full flex-col gap-3">
            <app-media-thumbnail-list class="w-full" [medias]="medias()" [noEditAndDragAndDrop]="true"></app-media-thumbnail-list>
        </div>
    </div>
</div>
