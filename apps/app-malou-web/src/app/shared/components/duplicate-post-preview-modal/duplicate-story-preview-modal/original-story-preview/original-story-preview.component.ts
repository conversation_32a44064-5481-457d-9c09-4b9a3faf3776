import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';

import { StoryToDuplicate } from ':modules/stories/v2/models/story-to-duplicate';
import { MediaThumbnailListComponent } from ':shared/components/media-thumbnail-list/media-thumbnail-list.component';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';

@Component({
    selector: 'app-original-story-preview',
    imports: [LazyLoadImageModule, TranslateModule, MediaThumbnailListComponent, PlatformLogoComponent],
    templateUrl: './original-story-preview.component.html',
    styleUrl: './original-story-preview.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OriginalStoryPreviewComponent {
    readonly story = input.required<StoryToDuplicate>();
    readonly storyIndex = input<number | undefined>(undefined);
    readonly totalPostCount = input<number | undefined>(undefined);

    readonly medias = computed(() => {
        const urls = this.story().getAllSmallestAttachmentUrls();
        return urls;
    });
}
