import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatOptionModule } from '@angular/material/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { LazyLoadImageModule } from 'ng-lazyload-image';
import { combineLatest } from 'rxjs';

import { getPlatformKeysWithStories, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { RestaurantsService } from ':core/services/restaurants.service';
import { PlatformOption } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/previews/previews.component';
import { PostDateStatus } from ':modules/posts/new-post-modal/types';
import { StoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/stories-previews.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { StoryToDuplicate } from ':modules/stories/v2/models/story-to-duplicate';
import { SchedulePostFormComponent } from ':shared/components/duplicate-post-preview-modal/components/schedule-post-form/schedule-post-form.component';
import { OriginalStoryPreviewComponent } from ':shared/components/duplicate-post-preview-modal/duplicate-story-preview-modal/original-story-preview/original-story-preview.component';
import { StoryPreviewCardComponent } from ':shared/components/duplicate-post-preview-modal/duplicate-story-preview-modal/story-preview-card/story-preview-card.component';
import { buildPlus15RoundedMinutesDate } from ':shared/components/duplicate-post-preview-modal/helpers/plus-15-minutes-date';
import { onlyFutureDate } from ':shared/components/duplicate-post-preview-modal/validators/publication-date-in-the-future';
import { BaseStepComponent } from ':shared/components/stepper-modal/base-step.component';
import { isPastHour } from ':shared/helpers/date';
import { Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration } from ':shared/pipes/illustration-path-resolver.pipe';

export interface DuplicateStoryInputData {
    selectedRestaurants: Restaurant[];
    index: number;
}

interface SharedData {
    storiesToDuplicate: StoryToDuplicate[];
    isDirectlyAfterUpsertPostModal?: boolean;
}

export interface DuplicateStoryForm {
    plannedPublicationDate: FormControl<Date>;
    postTime: FormControl<string>;
    keys: FormControl<PlatformKey[]>;
    status: FormControl<PostDateStatus>;
    // wont be visible in the UI
    restaurant: FormControl<Restaurant>;
    hasPlatformsConnected: FormControl<boolean>;
}

const BaseStepDuplicateStoryPreviewComponent = BaseStepComponent<DuplicateStoryInputData, SharedData>;

export interface DuplicateStoryPreviewModalSubmitData {
    restaurant: Restaurant;
    status: PostPublicationStatus;
    plannedPublicationDate?: Date;
    platformKeys: PlatformKey[];
}

@Component({
    selector: 'app-duplicate-story-preview-modal',
    imports: [
        NgTemplateOutlet,
        MatTooltipModule,
        MatDividerModule,
        MatIconModule,
        TranslateModule,
        ReactiveFormsModule,
        MatButtonModule,
        MatButtonModule,
        MatIconModule,
        FormsModule,
        MatProgressBarModule,
        MatProgressSpinnerModule,
        MatAutocompleteModule,
        MatOptionModule,
        MatFormFieldModule,
        MatMenuModule,
        MatCheckboxModule,
        MatRadioModule,
        MatTooltipModule,
        MatSelectModule,
        LazyLoadImageModule,
        SchedulePostFormComponent,
        OriginalStoryPreviewComponent,
        StoryPreviewCardComponent,
        StoriesPreviewsComponent,
    ],
    templateUrl: './duplicate-story-preview-modal.component.html',
    styleUrl: './duplicate-story-preview-modal.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DuplicateStoryPreviewModalComponent extends BaseStepDuplicateStoryPreviewComponent implements OnInit {
    readonly Illustration = Illustration;

    readonly customizedDatePostForm = new FormGroup(
        {
            status: new FormControl<PostDateStatus>(PostDateStatus.LATER),
            plannedPublicationDate: new FormControl<Date>(new Date()),
            postTime: new FormControl(''),
        },
        {
            validators: [onlyFutureDate()],
        }
    );
    readonly willPostAllAtSameTime = signal(true);

    readonly restaurant = inject(RestaurantsService).restaurantSelected$.value;
    private readonly _destroyRef = inject(DestroyRef);

    readonly previewCaptionPostForms = new FormArray<FormGroup<DuplicateStoryForm>>([]);

    readonly isStepValid = signal(false);

    storyToDuplicate: StoryToDuplicate;
    storyToDuplicateWithOptimizedCaption: StoryToDuplicate;

    storyForPreview: StoryItem;

    // TODO stories-v2 get platforms username and picture
    selectedPreviewPlatform: PlatformOption | null = null;

    readonly SvgIcon = SvgIcon;

    constructor() {
        super();
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.storyToDuplicate = this.sharedData.storiesToDuplicate[this.inputData.index];
        this.storyToDuplicateWithOptimizedCaption = new StoryToDuplicate(this.storyToDuplicate);
        this.storyForPreview = StoryItem.fromStoryToDuplicate(this.storyToDuplicate);
        this._initializePostForm();
        this._initializeFormsValidation();
    }

    onClose(): void {
        // TODO stories-v2 add an event emitter listened in stepper modal
    }

    protected _submitData(): DuplicateStoryPreviewModalSubmitData[] {
        return this.previewCaptionPostForms.controls
            .filter((form) => form.get('hasPlatformsConnected')?.value)
            .map((form) => {
                const { status, plannedPublicationDate } = this._buildPostStatusAndDate(form);

                return {
                    restaurant: form.get('restaurant')!.value,
                    status,
                    plannedPublicationDate,
                    platformKeys: form.get('keys')!.value,
                };
            });
    }

    private _buildPostStatusAndDate(form: FormGroup<DuplicateStoryForm>): { status: PostPublicationStatus; plannedPublicationDate: Date } {
        let plannedPublicationDate: Date;
        let status: PostPublicationStatus;

        if (!this.willPostAllAtSameTime()) {
            plannedPublicationDate = this._buildDate({
                plannedPublicationDate: form.get('plannedPublicationDate')!.value,
                postTime: form.get('postTime')!.value,
            });
            status = PostPublicationStatus.PENDING;
        } else {
            const statusForm = this.customizedDatePostForm.get('status')!.value;
            if (statusForm === PostDateStatus.NOW) {
                plannedPublicationDate = new Date();
                status = PostPublicationStatus.PENDING;
            } else {
                plannedPublicationDate = this._buildDate({
                    plannedPublicationDate: this.customizedDatePostForm.get('plannedPublicationDate')!.value!,
                    postTime: this.customizedDatePostForm.get('postTime')!.value!,
                });
                status = statusForm === PostDateStatus.DRAFT ? PostPublicationStatus.DRAFT : PostPublicationStatus.PENDING;
            }
        }
        return { status, plannedPublicationDate };
    }

    protected _isValid(): boolean {
        return this.isStepValid();
    }

    private _buildDate({ plannedPublicationDate, postTime }: { plannedPublicationDate: Date; postTime: string }): Date {
        const date = new Date(plannedPublicationDate);
        const [hours, minutes] = postTime.split(':').map((n) => parseInt(n, 10));
        date.setHours(hours);
        date.setMinutes(minutes);
        return date;
    }

    private _initializePostForm(): void {
        const postDate = this.storyToDuplicate.plannedPublicationDate ?? new Date();
        const status = this.storyToDuplicate.published === PostPublicationStatus.PENDING ? PostDateStatus.LATER : PostDateStatus.DRAFT;
        if (postDate >= new Date()) {
            const postTime = buildPlus15RoundedMinutesDate(postDate).toFormat('HH:mm');
            this.customizedDatePostForm.patchValue({
                status,
                plannedPublicationDate: postDate,
                postTime,
            });
        } else {
            const plannedPublicationDate = buildPlus15RoundedMinutesDate();
            this.customizedDatePostForm.patchValue({
                status,
                plannedPublicationDate: plannedPublicationDate.toJSDate(),
                postTime: plannedPublicationDate.toFormat('HH:mm'),
            });
        }

        this._buildPreviewStoryForms(
            // TODO stories-v2 get connected platforms
            this.inputData.selectedRestaurants.map((restaurant) => ({ restaurantId: restaurant._id, keys: getPlatformKeysWithStories() }))
        );
    }

    private _initializeFormsValidation(): void {
        combineLatest([this.customizedDatePostForm.valueChanges, this.previewCaptionPostForms.valueChanges])
            .pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe(() => {
                this.isStepValid.set(this._areFormsValid());
                this.valid.emit(this.isStepValid());
            });
    }

    private _areFormsValid(): boolean {
        let isPlannedPublicationDateValid = false;

        if (this.willPostAllAtSameTime()) {
            isPlannedPublicationDateValid = this.customizedDatePostForm.valid;
        } else {
            isPlannedPublicationDateValid = this.previewCaptionPostForms.controls.every((postForm) => {
                const plannedPublicationDate = postForm.controls['plannedPublicationDate'].value;
                const postTime = postForm.controls['postTime'].value;
                return !isPastHour({ hourWithMinute: postTime, date: plannedPublicationDate });
            });
        }

        return isPlannedPublicationDateValid;
    }

    private _buildPreviewStoryForms(restaurantIdsWithKeys: { keys: PlatformKey[]; restaurantId: string }[]): void {
        const restaurantsWithKeys = restaurantIdsWithKeys
            .map((data) => {
                const restaurant = this.inputData.selectedRestaurants.find((rest) => rest._id === data.restaurantId);
                return {
                    restaurant,
                    keys: data.keys,
                };
            })
            .filter((data) => data.restaurant);

        const status = this.storyToDuplicate.published === PostPublicationStatus.PENDING ? PostDateStatus.LATER : PostDateStatus.DRAFT;

        for (const restaurantWithKeys of restaurantsWithKeys) {
            const keys: PlatformKey[] = restaurantWithKeys.keys;
            const hasPlatformsConnected = keys.length > 0;

            const form = new FormGroup(
                {
                    plannedPublicationDate: new FormControl<Date>(this.customizedDatePostForm.get('plannedPublicationDate')!.value!, {
                        nonNullable: true,
                    }),
                    postTime: new FormControl(this.customizedDatePostForm.get('postTime')!.value!, { nonNullable: true }),
                    keys: new FormControl<PlatformKey[]>(keys, { nonNullable: true }),
                    status: new FormControl<PostDateStatus>(status, { nonNullable: true }),
                    // wont be visible in the UI
                    restaurant: new FormControl<Restaurant>(restaurantWithKeys.restaurant!, { nonNullable: true }),
                    hasPlatformsConnected: new FormControl<boolean>(hasPlatformsConnected, { nonNullable: true }),
                },
                {
                    validators: [onlyFutureDate()],
                }
            );
            this.previewCaptionPostForms.push(form);
            if (!hasPlatformsConnected) {
                form.disable();
            }
        }
    }
}
