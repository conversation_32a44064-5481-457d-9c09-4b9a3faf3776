# Malou API

## Table of content

- [Tests (Jest)](#tests-jest)
    - [Units tests](#units-tests)
    - [Integrations tests](#integrations-tests)
- [How to add queue](#how-to-add-queue)
    - [Local](#local)
    - [Deploy your queue](#deploy-your-queue)
- [How to add a job (cron)](#how-to-add-a-job-cron)
- [How to add a webhook with ngrok](#how-to-add-a-webhook-with-ngrok)
- [Activate monitoring](#activate-monitoring)
- [How to handle HEIC images in local environment](#how-to-handle-heic-images-in-local-environment)
- [Ffmpeg installation](#ffmpeg-installation)

## Tests (Jest)

### Units tests

To run all units tests run the following command :

- `pnpm run test:unit`

To run one specific file run the following command :
(Btw if you just want to run one test add **.only** after **it** keyword)

- `pnpm run test:unit:single /path/to/example.unit.test.(js,ts)`

### Integrations tests

Make sure containers redis & elasticmq are running.
Make sure you have **.env.jest.tests** at the root directory.

To run integration tests run the following command :

- `pnpm run test:integration`

To run one specific file run the following command :

- `pnpm run test:integration:single /path/to/example.int.test.js`

## How to add queue

### Local

Queue is used to send message of various size asynchronously.
In local, we use elasticmq to imitate aws sqs behaviour.
We use multiple queues to do a specific action across the app like publish posts or collect reviews.

To add a queue proceed like this :

```
## in elasticmq.conf
my_new_sqs_queue {
    defaultVisibilityTimeout = 10 seconds
    delay = 2 seconds
    receiveMessageWait = 0 seconds
}
```

In your .env.default add queue like this :

```
## .env.default
NEW_QUEUE=http://localhost:9324/000000000000/my_new_sqs_queue
```

Then, create a new folder in ./src/agenda-jobs/sqs/<new_folder> and create **receive.js** and **send.js** depending on your needs

You can see your local queues with the elasticMq UI on : localhost:9325

### Deploy your queue

To deploy your queue go [here](https://eu-west-3.console.aws.amazon.com/sqs/v2/home?region=eu-west-3#/queues)

## How to add a job (cron)

Jobs are scheduled tasks launched by the **worker**, we use [agenda](https://www.npmjs.com/package/agenda) library.

To add a job proceed like this :

- create a file in src/agenda-jobs/jobs/<your_new_job>.ts and define your job like below

```js
@singleton()
export class CustomJobs extends AbstractJobs {
    init(agenda: Agenda): void {
        agenda.define('my job 1', async (job: Job) => {
            // your job logic
        });

        agenda.define('my job 2', async (job: Job) => {
            // your job logic
        });
        logger.info('[CustomJobs] - Initialized');
    }
}
```

- Then load your job in agenda.ts like this

```
// ...
container.resolve(CustomJobs).init(agenda);
// ...
```

- If it's a cron job, you can add beloww

```js
// ...
await agenda.cancel({ name: 'example job' });
await agenda.every('0 21 * * 6', 'example job', {}, { timezone: 'Europe/Paris' }); // saturday 9pm
// ...
```

if you are not familiar with cron syntax, check these links [cheat sheet](https://devhints.io/cron) & [generator cron](https://crontab.guru/)

## How to add a webhook with ngrok

Refer to this [notion link](https://www.notion.so/welcomehomemalou/Webhooks-local-testing-setup-fb31f43a9af44e9ba1e21e3cb27b1580)

connect to <NAME_EMAIL>, ask if you don't have access
(we use this account to don't have to pay premium subscription 🐀 )

## Activate monitoring

Monitoring is activated in production mode to collect metrics about our API and worker. It can also be activated in development mode for api debugging purposes or testing our monitoring setup.

To connect the API with the monitoring stack, add this to your `.env.default` file:

```
############################# MONITORING
OPEN_TELEMETRY_COLLECTOR_HOST=localhost
```

This will allow our API and worker to send metrics to our local monitoring stack. Without this, metrics are sent to the console. Logging is disabled by default, but if you want to see the metrics in the console, you can set `SHOULD_LOG_METRICS` to `true` in `metrics.service.ts`.

Then run

```
pnpm run start-monitoring
```

This script will start our monitoring infrastructure (using Docker Compose). It is composed of

- an [OpenTelemetry Collector using the AWS distro](https://aws-otel.github.io/docs/introduction): it will receive metrics from our API / worker and send them to Prometheus on push mode
- a [Prometheus server](https://prometheus.io/): an open source monitoring solution that will store our metrics. You can also query them from its interface. Available at [http://localhost:5050](http://localhost:5050)
- [Grafana](https://grafana.com/) to visualize and create dashboards from Prometheus metrics. Available at [http://localhost:5051](http://localhost:5051). You must use admin / admin as username / password to connect for the first time

## How to handle HEIC images in local environment

### Why

The app is designed to handle heic images, but it requires some additional configuration that are already made in the deployed version of the app (ie: the Dockerfile) but not made in local environment.

This additional configuration is needed for Sharp package because it does not handle heic image by default.

More precisely, Sharp uses libvips under the hood to make image transformation, but the libvips binary shipped with Sharp does not support HEIC, so we need to:

- Install a version of libvips with HEIC support globally on the system
- [Build Sharp](https://sharp.pixelplumbing.com/install#building-from-source) against this version of libvips (if everything has been configured correctly this should be automatic during `pnpm i` or `pnpm add sharp` or `pnpm rebuild sharp`)

Currently, the version of Sharp is 0.33.1 so libvips must be >= is 8.15.0 [source](https://github.com/lovell/sharp/blob/v0.33.1/package.json#L190)
and you can see in the Dockerfile, libvips is installed with version 8.15.0-r0.

So later in the Dockerfile when we run `pnpm i` Sharp will detect and use the system installed libvips binary instead of its own libvips binary.

### How

- Check which version of libvips Sharp needs :
    - go to Sharp [github](https://github.com/lovell/sharp)
    - choose your branch version
    - go to package.json and check under `config` or `engines`
- If your package manager (apt, apk, brew, ...) got the wanted libvips version, follow [Case 1](#case-1), else follow [Case 2](#case-2)
- `pnpm rebuild Sharp`
    - It should output `<some path> Running install script, done in 6.9s`
    - If there is an error, you can see the logs by re-running the script like this :
        - `cd node_modules/sharp`
        - `pnpm run install`
- `cd to root repo` and `node scripts/test-sharp-libvips.test.js`  
  If this does not throw you are good !

#### Case 1

Replace apt with your package manager in the following commands.  
The packages names can be a little different depending on your package manager.

- `apt install libvips-dev libheif-dev`  
  (you might want to specify the version : `libvips-dev=x.x.x libheif-dev=x.x.x`)
- Follow the last instructions in the part [How](#how)

#### Case 2

You will need to manually build libvips from source.

- Create a folder (you will prefer to keep it to not redo this part in the future)
- Follow this [tutorial](https://www.libvips.org/install.html#building-libvips-from-source) to build libvips.
- `export PKG_CONFIG_PATH=<path-to-libvips-install>/lib/<os-dependent-path>/pkgconfig`  
  Explanation :
    - Sharp use pkg-config to find libvips so we set the PKG_CONFIG_PATH env to tell pkg-config where libvips is.
    - The path may vary depending on your OS, the important thing is that the folder must contains `vips-cpp.pc` and `vips.pc`
- Follow the last instructions in the part [How](#how)

## Ffmpeg installation

You need ffmpeg if you want to run the app locally (see MultimediaStreamsInformationServiceFfprobeAdapter class).

**Installation**: via your package manager or [ffmpeg website](https://ffmpeg.org/download.html).  
**Version**: keep as close as possible as the version used in the Dockerfile.
