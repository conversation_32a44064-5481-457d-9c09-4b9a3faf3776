import axios, { AxiosInstance } from 'axios';
import qs from 'qs';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { ProviderMetricsService } from ':providers/provider.metrics.service';
import {
    PublishPhotosInitRequestBody,
    PublishPhotosInitResponseBody,
    PublishVideoInitRequestBody,
    PublishVideoInitResponseBody,
    QueryCreatorInfoResponse,
    TiktokResponse,
    UserInfoQueryField,
    UserInfoResponse,
} from ':providers/tiktok/validators';
import { allVideoKeys, VideoObject, VideoObjectQueryField } from ':providers/tiktok/validators/display-api.validators';
import {
    AccessTokenResponseBody,
    FetchAccessTokenRequestBody,
    RefreshAccessTokenRequestBody,
} from ':providers/tiktok/validators/oauth.validators';

@singleton()
export class TiktokProvider {
    private _axiosInstance: AxiosInstance;

    constructor(private readonly _providerMetricsService: ProviderMetricsService) {
        this._axiosInstance = axios.create({
            baseURL: Config.platforms.tiktok.api.baseUri,
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Cache-Control': 'no-cache',
            },
        });
    }

    async fetchAccessToken({ code }: { code: string }) {
        const requestBody: FetchAccessTokenRequestBody = {
            client_key: Config.platforms.tiktok.api.clientId,
            client_secret: Config.platforms.tiktok.api.clientSecret,
            code,
            redirect_uri: Config.platforms.tiktok.api.redirectUri,
            grant_type: 'authorization_code',
        };

        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'tiktok',
            requestId: 'tiktok.token.fetch',
            request: () => this._axiosInstance.post<AccessTokenResponseBody>(`oauth/token/`, qs.stringify(requestBody)),
        });

        return data;
    }

    async refreshAccessToken({ token }: { token: string }) {
        const requestBody: RefreshAccessTokenRequestBody = {
            client_key: Config.platforms.tiktok.api.clientId,
            client_secret: Config.platforms.tiktok.api.clientSecret,
            grant_type: 'refresh_token',
            refresh_token: token,
        };

        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'tiktok',
            requestId: 'tiktok.token.refresh',
            request: () =>
                this._axiosInstance.post<AccessTokenResponseBody>(
                    `${Config.platforms.tiktok.api.baseUri}/oauth/token/`,
                    qs.stringify(requestBody)
                ),
        });

        return data;
    }

    // This should remain an arrow function for the context to propagate when calling CallTiktokApiService.execute with this method
    // https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info
    // Scopes: user.info.basic, user.info.profile, user.info.stats
    getUserInfo = async ({ accessToken, fields }: { accessToken: string; fields: UserInfoQueryField[] }) => {
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'tiktok',
            requestId: 'tiktok.user.info',
            request: () =>
                this._axiosInstance.get<TiktokResponse<{ user: UserInfoResponse }>>(
                    `${Config.platforms.tiktok.api.baseUri}/user/info/?fields=${fields.join(',')}`,
                    {
                        headers: { Authorization: `Bearer ${accessToken}` },
                    }
                ),
        });
        return data;
    };

    // This should remain an arrow function for the context to propagate when calling CallTiktokApiService.execute with this method
    // https://developers.tiktok.com/doc/content-posting-api-reference-query-creator-info
    // Scope: video.publish
    // Note: Each user access_token is limited to 20 requests per minute.
    queryCreatorInfo = async ({ accessToken }: { accessToken: string }) => {
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'tiktok',
            requestId: 'tiktok.query.creator.info',
            request: () =>
                this._axiosInstance.post<TiktokResponse<QueryCreatorInfoResponse>>(
                    `${Config.platforms.tiktok.api.baseUri}/post/publish/creator_info/query/`,
                    {},
                    {
                        headers: {
                            'Content-Type': 'application/json; charset=UTF-8',
                            Authorization: `Bearer ${accessToken}`,
                        },
                    }
                ),
        });

        return data;
    };

    // This should remain an arrow function for the context to propagate when calling CallTiktokApiService.execute with this method
    // Scopes: video.publish or video.upload
    // Note: Each user access_token is limited to 6 requests per minute.
    // https://developers.tiktok.com/doc/content-posting-api-reference-photo-post
    initPostPublication = async ({ accessToken, body }: { accessToken: string; body: PublishPhotosInitRequestBody }) => {
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'tiktok',
            requestId: 'tiktok.publish.photos.init',
            request: () =>
                this._axiosInstance.post<TiktokResponse<PublishPhotosInitResponseBody>>(
                    `${Config.platforms.tiktok.api.baseUri}/post/publish/content/init/`,
                    body,
                    {
                        headers: {
                            'Content-Type': 'application/json; charset=UTF-8',
                            Authorization: `Bearer ${accessToken}`,
                        },
                    }
                ),
        });

        return data;
    };

    // This should remain an arrow function for the context to propagate when calling CallTiktokApiService.execute with this method
    // Scope: video.publish
    // Note: Each user access_token is limited to 6 requests per minute.
    // https://developers.tiktok.com/doc/content-posting-api-reference-direct-post
    initVideoPublication = async ({ accessToken, body }: { accessToken: string; body: PublishVideoInitRequestBody }) => {
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'tiktok',
            requestId: 'tiktok.publish.video.init',
            request: () =>
                this._axiosInstance.post<TiktokResponse<PublishVideoInitResponseBody>>(
                    `${Config.platforms.tiktok.api.baseUri}/post/publish/video/init/`,
                    body,
                    {
                        headers: {
                            'Content-Type': 'application/json; charset=UTF-8',
                            Authorization: `Bearer ${accessToken}`,
                        },
                    }
                ),
        });

        return data;
    };

    // This should remain an arrow function for the context to propagate when calling CallTiktokApiService.execute with this method
    // https://developers.tiktok.com/doc/tiktok-api-v2-video-query
    // Scopes: video.list
    queryVideos = async ({ accessToken, fields, ids }: { accessToken: string; fields?: VideoObjectQueryField[]; ids: string[] }) => {
        const fieldsToPick = (fields ?? allVideoKeys).join(',');
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'tiktok',
            requestId: 'tiktok.videos.query',
            request: () =>
                this._axiosInstance.post<TiktokResponse<{ videos: VideoObject[] }>>(
                    `${Config.platforms.tiktok.api.baseUri}/video/query/?fields=${fieldsToPick}`,
                    {
                        filters: {
                            video_ids: ids,
                        },
                    },
                    {
                        headers: {
                            Authorization: `Bearer ${accessToken}`,
                            'Content-Type': 'application/json',
                        },
                    }
                ),
        });

        return data;
    };

    // This should remain an arrow function for the context to propagate when calling CallTiktokApiService.execute with this method
    // https://developers.tiktok.com/doc/tiktok-api-v2-video-list
    // Scopes: video.list
    getAllVideos = async ({
        accessToken,
        fields,
        maxCount = 2000,
    }: {
        accessToken: string;
        fields?: VideoObjectQueryField[];
        maxCount?: number;
    }) => {
        const fieldsToPick = (fields ?? allVideoKeys).join(',');
        const requestPage = (cursor?: string) =>
            this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'tiktok',
                requestId: 'tiktok.videos.list',
                request: () =>
                    this._axiosInstance.post<TiktokResponse<{ videos: VideoObject[]; cursor?: string; has_more: boolean }>>(
                        `${Config.platforms.tiktok.api.baseUri}/video/list/?fields=${fieldsToPick}`,
                        {
                            max_count: 20, // TikTok max per request is 20
                            ...(cursor ? { cursor } : {}),
                        },
                        {
                            headers: {
                                Authorization: `Bearer ${accessToken}`,
                                'Content-Type': 'application/json',
                            },
                        }
                    ),
            });

        const { data: firstPage } = await requestPage();
        let timestampCursor = firstPage.data.cursor;
        let hasMore = firstPage.data.has_more;
        const allVideos: VideoObject[] = [...firstPage.data.videos];

        while (hasMore && allVideos.length < maxCount) {
            const currentCursor = timestampCursor;
            const { data: nextPage } = await requestPage(currentCursor);

            allVideos.push(...nextPage.data.videos);
            timestampCursor = nextPage.data.cursor;
            hasMore = nextPage.data.has_more;
        }

        return {
            data: {
                cursor: timestampCursor,
                has_more: hasMore,
                videos: allVideos,
            },
        };
    };
}
