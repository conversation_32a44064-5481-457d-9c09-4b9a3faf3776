import axios from 'axios';
import { FB as Meta } from 'fb';
import { uniqBy } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { facebookPageAccessTokenErrorCodes, isFulfilled, isNotNil } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { FacebookCredential } from ':modules/credentials/platforms/facebook/entities/facebook-credential.entity';
import {
    IFacebookAccount,
    IFacebookAccountAggregation,
    IFacebookDataWithPaging,
    IFacebookUser,
} from ':modules/credentials/platforms/facebook/facebook.interface';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { RefreshTokenResponse } from ':providers/meta/facebook/facebook-api-provider.interface';
import { ApiTimeIntervalParams, DailyMetricsTimeSeriesInsights, MetaApiCallParams } from ':providers/meta/meta-api-provider.interface';

@singleton()
export default class FacebookApiProvider {
    // 2108006 -> "Contenu multimédia publié avant la conversion en compte business"
    // This error subcode only show up when the endpoint return only bad post
    // If our research contains bad post and good post, this error does not show up, so we can just ignore it
    private readonly MEDIA_ERROR_SUBCODE_TO_IGNORE = 2108006;

    constructor(private readonly _facebookCredentialsRepository: FacebookCredentialsRepository) {
        logger.info('MetaProvider initialized');
        Meta.options({
            version: Config.platforms.facebook.api.apiVersion,
            appId: Config.platforms.facebook.api.appId,
            redirectUri: Config.platforms.facebook.api.redirectUri,
            timeout: Config.platforms.facebook.api.timeoutThresholdInMs,
        });
    }

    getMetaClient() {
        return Meta;
    }

    /** USER ACCOUNTS */

    // TODO: this is not really a provider method
    async getUserAccountsAndStores(userAccessToken: string): Promise<IFacebookAccountAggregation> {
        const result = await this.refreshUserAccessToken(userAccessToken);

        const userFields = 'id,name';
        const accountFields =
            'has_transitioned_to_new_page_experience,picture,overall_star_rating,location,name,link,access_token,instagram_business_account{name,biography,profile_picture_url,username},store_location_descriptor,parent_page';
        const limit = 2000;

        // Getting user with his accounts
        const endpoint = `/me?fields=${userFields},accounts.limit(${limit}){${accountFields}}`;
        const response = await this._callMetaApi({
            endpoint,
            method: 'get',
            params: {},
            token: result.access_token ?? '',
        });
        const { id: userId, name: userName } = response;

        const accounts: IFacebookAccount[] = [response]
            .map((user: IFacebookUser) => user.accounts?.data)
            .filter(isNotNil)
            .reduce((acc, cur) => acc.concat(cur), []);

        const locationAccounts = await this._getLocationsForAccounts(accounts);

        if (locationAccounts?.length) {
            accounts.push(...locationAccounts);
        }

        return {
            id: userId,
            name: userName,
            data: uniqBy(accounts, 'id'),
        };
    }

    private async _getLocationsForAccounts(accounts: IFacebookAccount[]): Promise<IFacebookAccount[]> {
        try {
            const parentPageCandidates = accounts.filter((account) => !account.store_location_descriptor);
            const results = await Promise.allSettled(
                parentPageCandidates.map((account) =>
                    this._callMetaApi({
                        // eslint-disable-next-line max-len
                        endpoint: `/${account.id}?fields=locations{id,store_location_descriptor,name,link,location,access_token,has_transitioned_to_new_page_experience,picture}`,
                        method: 'get',
                        params: {},
                        token: account.access_token ?? '', // we need to fetch potential locations using page access token otherwise it will fail
                    })
                )
            );
            const locationAccounts = results
                .filter(isFulfilled)
                .map((r) => r.value)
                .filter((v) => !!v.locations?.data?.length)
                .flatMap((acc) => acc.locations.data)
                .filter((locationAccount) => !!locationAccount.access_token);
            return locationAccounts;
        } catch (error) {
            logger.warn('[FB_FAILED_TO_FETCH_LOCATIONS]', { error });
            return [];
        }
    }

    async fetchFacebookMultiDailyMetrics(
        pageAccessToken: string,
        pageId: string,
        metrics: string[],
        insightsTimeInterval: ApiTimeIntervalParams
    ): Promise<DailyMetricsTimeSeriesInsights> {
        const { since, until } = insightsTimeInterval;
        const endpoint = `${pageId}/insights`;
        const method = 'get';
        const params = {
            metric: metrics.join(','),
            period: 'day',
            since,
            until,
        };
        return this._callMetaApi({
            endpoint,
            method,
            params,
            token: pageAccessToken,
        });
    }

    async getPostMedia({
        credential,
        pageId,
        pageAccessToken,
        mediaId,
    }: {
        credential: FacebookCredential;
        pageId: string;
        pageAccessToken: string;
        mediaId: string;
    }) {
        const endpoint = mediaId;
        const method = 'get';
        const params = {
            fields: 'id,from,created_time,updated_time,published,privacy,post_id',
        };
        return this._callWithPageAccessToken({
            credential,
            pageId,
            endpoint,
            method,
            params,
            pageAccessToken,
        });
    }

    async getPagePosts({
        credential,
        pageId,
        pageAccessToken,
        recentPostsOnly,
    }: {
        credential: FacebookCredential;
        pageId: string;
        pageAccessToken: string;
        recentPostsOnly: boolean;
    }): Promise<{ posts: { data: FbPostData[] } }> {
        const endpoint = pageId;
        const method = 'get';
        const params = {
            fields: `posts{
                id,
                is_published,
                message,
                created_time,
                updated_time,
                permalink_url,
                story,
                status_type,
                attachments{
                    subattachments,
                    media
                },
                comments{
                    like_count,
                    id,
                    from,
                    message,
                    created_time,
                    comments{
                        like_count,
                        id,
                        from,
                        message,
                        created_time
                    }
                },
                insights.metric(post_impressions),
                shares,
                likes.summary(true).limit(0)
            }`,
        };
        const res = await this._callWithPageAccessToken({
            credential,
            pageId,
            endpoint,
            method,
            params,
            pageAccessToken,
        });
        let posts: FbPostData[] = res?.posts?.data || [];
        if (recentPostsOnly) {
            return { posts: { data: posts } };
        }
        let next = res?.posts?.paging?.next;
        let count = 0;
        const fetchPostsDepth = parseInt(Config.platforms.facebook.api.nextCount, 10);
        if (Number.isNaN(fetchPostsDepth)) {
            throw new Error('Invalid fetchPostsDepth configuration value');
        }
        while (count < fetchPostsDepth && next) {
            const result = await axios.get(next);
            const {
                data: { data, paging },
            } = result;
            posts = posts.concat(data);
            next = paging.next;
            count += 1;
        }
        return { posts: { data: posts } };
    }

    async getReels(
        pageAccessToken: string,
        pageId: string,
        filters?: { startDate: Date; endDate: Date }
    ): Promise<FacebookApiTypes.Reels.GetReelWithInsightsResponse[]> {
        return this._getReels<true>(pageAccessToken, pageId, true, filters);
    }

    async _getReels<T>(
        pageAccessToken: string,
        pageId: string,
        withInsights: T,
        filters?: { startDate: Date; endDate: Date }
    ): Promise<(T extends true ? FacebookApiTypes.Reels.GetReelWithInsightsResponse : FacebookApiTypes.Reels.GetReelResponse)[]> {
        const isReadyReel = (reel: FacebookApiTypes.Reels.GetReelResponse) =>
            reel.status.video_status === FacebookApiTypes.Videos.VideoStatus.READY;

        const since = filters?.startDate ? Math.round(filters.startDate.getTime() / 1000) : undefined;
        const until = filters?.endDate ? Math.round(filters.endDate.getTime() / 1000) : undefined;
        const endpoint = `${pageId}/video_reels`;
        const method = 'get';
        let fields = 'id,created_time,updated_time,title,description,source,permalink_url,status,picture,thumbnails';
        if (withInsights) {
            fields += ',video_insights';
        }
        const params = {
            fields,
            limit: 50,
            since,
            until,
        };
        const token = pageAccessToken;

        const res: IFacebookDataWithPaging<any> = await this._callMetaApi({
            endpoint,
            method,
            params,
            token,
        });
        const reels = res.data?.filter(isReadyReel) ?? [];
        let next = res.paging?.next;
        let count = 0;
        while (next && count < +Config.platforms.facebook.api.nextCount) {
            const axiosRes = await axios.get<IFacebookDataWithPaging<any>>(next);
            reels.push(...(axiosRes.data.data?.filter(isReadyReel) ?? []));
            next = axiosRes.data.paging?.next;
            count += 1;
        }
        return reels;
    }

    /** REFRESH TOKENS */

    refreshUserAccessToken(userAccessToken: string): Promise<RefreshTokenResponse> {
        return Meta.api('oauth/access_token', {
            client_id: Config.platforms.facebook.api.appId,
            client_secret: Config.platforms.facebook.api.appSecret,
            grant_type: 'fb_exchange_token',
            fb_exchange_token: userAccessToken,
        });
    }

    refreshPageAccessToken(pageId: string, userAccessToken: string): Promise<RefreshTokenResponse> {
        return this._callMetaApi({
            endpoint: pageId,
            method: 'get',
            params: { fields: 'access_token' },
            token: userAccessToken,
        });
    }

    /** API CALL */

    private _callMetaApi({ endpoint, method, params, token }: MetaApiCallParams): Promise<any> {
        return new Promise((resolve, reject) => {
            Meta.api(endpoint, method, { ...params, access_token: token }, (res) => {
                if (res.error) {
                    reject(res.error);
                }
                resolve(res);
            });
        });
    }

    private _callWithPageAccessToken = async ({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    }: {
        credential: FacebookCredential;
        pageId: string;
        endpoint: string;
        method: string;
        params: Record<string, any>;
        pageAccessToken: string;
    }) => {
        try {
            const res = await this._callMetaApi({
                endpoint,
                method,
                params,
                token: pageAccessToken,
            });
            await this._facebookCredentialsRepository.findOneAndUpdate({
                filter: {
                    _id: toDbId(credential.id),
                    'pageAccess.pageAccessToken': pageAccessToken,
                },
                update: {
                    'pageAccess.$.lastSeenWorking': new Date(),
                },
            });
            return res;
        } catch (error: Error | any) {
            if (this.MEDIA_ERROR_SUBCODE_TO_IGNORE === error.error_subcode) {
                return { data: [] };
            }
            if (!facebookPageAccessTokenErrorCodes.includes(error.code)) {
                try {
                    const res = await this._callMetaApi({
                        // try using directly userAccessToken, it seems that for most endpoints userAccessToken is enough
                        // and it can happen that pageAccessToken does not work even though we have the right authorizations
                        endpoint,
                        method,
                        params,
                        token: credential.userAccessToken,
                    });
                    return res;
                } catch (e: Error | any) {
                    logger.info('[FB_CREDENTIAL] Failed with backup user access token strategy - ', {
                        error: e?.message ?? e,
                        pageId,
                        endpoint,
                        method,
                        params,
                    });
                }
                throw error;
            }

            // if pageId is a fbPageId it will just return the same id
            const fbPageId = credential.getFbPageIdBySocialId(pageId);
            assert(fbPageId, 'Missing fbPageId');

            const res = await this.refreshPageAccessToken(fbPageId, credential.userAccessToken);
            const newPageAccessToken = res.access_token || pageAccessToken;
            if (!res.access_token) {
                logger.error('[FB_CREDENTIAL] Cannot refresh page access token');
            }
            await this._facebookCredentialsRepository.findOneAndUpdate({
                filter: {
                    _id: toDbId(credential.id),
                    'pageAccess.pageAccessToken': pageAccessToken,
                },
                update: {
                    'pageAccess.$.pageAccessToken': newPageAccessToken,
                    'pageAccess.$.lastSeenWorking': new Date(),
                },
            });
            return this._callMetaApi({
                endpoint,
                method,
                params,
                token: newPageAccessToken,
            });
        }
    };
}
