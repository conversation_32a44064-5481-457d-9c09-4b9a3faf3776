import 'reflect-metadata';

import ':env';

import { Cursor } from 'mongoose';
import { container, singleton } from 'tsyringe';

import { DbId, IStoryPostInsight } from '@malou-io/package-models';
import { MalouMetric, PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

import { PostInsightsRepository } from ':modules/post-insights/post-insights.repository';
import StoryInsightsRepository from ':modules/post-insights/v2/repositories/story-insights.repository';

interface IOldPostInsight {
    _id: DbId;
    platformKey: PlatformKey;
    socialId: string;
    date: string;
    data: {
        metric: StoryMetric;
        value: number | null;
    }[];
    platformSocialId: string;
    socialLink?: string;
    createdAt: string;
    updatedAt: string;
}

type StoryMetric =
    | MalouMetric.IMPRESSIONS
    | MalouMetric.REACH
    | MalouMetric.TAPS_FORWARD
    | MalouMetric.TAPS_BACK
    | MalouMetric.REPLIES
    | MalouMetric.TAPS_EXITS
    | 'exits';

interface IStoryInsightData {
    impressions: number;
    reach: number;
    taps_forward: number;
    taps_back: number;
    replies: number;
    taps_exits: number;
}

@singleton()
class MigrateStoryInsightsSchemaTask {
    constructor(
        private readonly _postInsightRepository: PostInsightsRepository,
        private readonly _storyInsightsRepository: StoryInsightsRepository
    ) {}

    async run(): Promise<void> {
        const cursor: Cursor<IOldPostInsight, never> = await this._postInsightRepository
            .aggregate([{ $match: { entityType: { $exists: false } } }])
            .cursor();

        await cursor.eachAsync(
            async (postInsights) => {
                console.log(`Migrating ${postInsights.length} post insights to story insights...`);
                const storyPostInsights: Omit<IStoryPostInsight, '_id'>[] = postInsights.map((postInsight) =>
                    this._mapToStoryPostInsight(postInsight)
                );

                const bulkOps = storyPostInsights.map((storyPostInsight) => ({
                    insertOne: {
                        document: storyPostInsight,
                    },
                }));

                await this._storyInsightsRepository.bulkOperations({ operations: bulkOps });
            },
            {
                batchSize: 1000,
            }
        );
    }

    private _mapToStoryPostInsight(postInsight: IOldPostInsight): Omit<IStoryPostInsight, '_id'> {
        const metricsObject = this._transformMetricsArrayToObject(postInsight.data);
        return {
            platformKey: postInsight.platformKey,
            socialId: postInsight.socialId,
            platformSocialId: postInsight.platformSocialId,
            data: metricsObject,
            entityType: PostInsightEntityType.STORY,
            lastFetchedAt: new Date(),
            postSocialCreatedAt: new Date(postInsight.date),
            createdAt: new Date(postInsight.createdAt),
            updatedAt: new Date(postInsight.updatedAt),
        };
    }

    private _transformMetricsArrayToObject(metricsArray: { metric: StoryMetric; value: number | null }[]): IStoryInsightData {
        return metricsArray.reduce((acc, { metric, value }) => {
            if (metric === 'exits') {
                acc.taps_exits = value ?? 0;
                return acc;
            }
            acc[metric] = value ?? 0;
            return acc;
        }, {} as IStoryInsightData);
    }
}

const task = container.resolve(MigrateStoryInsightsSchemaTask);

task.run()
    .then(() => {
        console.log('Migration completed successfully.');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Migration failed:', error);
        process.exit(1);
    });
