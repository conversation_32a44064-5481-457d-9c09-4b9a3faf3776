import ffprobe from 'ffprobe';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from '../classes/malou-error';
import { logger } from '../logger';
import { MultimediaStreamsInformationService, VideoStreamInformation } from './multimedia-streams-information-service';

/**
 * This service requires ffmpeg to be installed on the system.
 *
 * It is actually installed in the Dockerfile for the deployed version of the app.
 * If you want test it locally, instal ffmpeg  with your package manager (preferably the same version as specified in the Dockerfile)
 */
@singleton()
export class MultimediaStreamsInformationServiceFfprobeAdapter implements MultimediaStreamsInformationService {
    async getVideoStreamInformation(localPathOrUrl: string): Promise<VideoStreamInformation> {
        const result = await this._run(localPathOrUrl);
        const videoStream = result.streams.find((stream) => stream.codec_type === 'video');
        assert(videoStream);
        if (!videoStream.height || !videoStream.width || !videoStream.duration_ts || !videoStream.duration || !videoStream.bit_rate) {
            throw new MalouError(MalouErrorCode.MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION, {
                message: 'Missing video stream information',
            });
        }
        return {
            height: videoStream.height,
            width: videoStream.width,
            durationInSeconds: parseFloat(videoStream.duration),
            bitrate: parseInt(videoStream.bit_rate.toString(), 10),
        };
    }

    async isHdrVideo(localPathOrUrl: string): Promise<boolean> {
        const start = Date.now();
        try {
            const result = await this._run(localPathOrUrl);
            const videoStream = result.streams.find((stream) => stream.codec_type === 'video');
            assert(videoStream);

            const transfer = videoStream.color_transfer?.toLowerCase();
            const primaries = videoStream.color_primaries?.toLowerCase();
            const colorSpace = videoStream.color_space?.toLowerCase();

            const isHdr =
                transfer === 'smpte2084' || // PQ transfer (HDR10, Dolby Vision)
                transfer === 'arib-std-b67' || // HLG transfer
                !!(primaries === 'bt2020' && colorSpace?.startsWith('bt2020'));

            return isHdr;
        } finally {
            const elapsedMs = Date.now() - start;
            logger.info('[MultimediaStreamsInformationServiceFfprobeAdapter.isHdrVideo] executed in', {
                localPathOrUrl,
                elapsedMs,
            });
        }
    }

    private async _run(localPathOrUrl: string): Promise<ffprobe.FFProbeResult> {
        return ffprobe(localPathOrUrl, { path: 'ffprobe' });
    }
}
