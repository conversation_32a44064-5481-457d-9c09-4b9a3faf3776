import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { DbId, IReview, newDbId, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    ApplicationLanguage,
    getApplicationLanguageDisplayName,
    isNotNilOrEmpty,
    MalouErrorCode,
    PlatformKey,
    PlatformPresenceStatus,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';
import { AiReviewsService, AiTranslationsText } from ':microservices/ai-reviews.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall, getSignatureAndCatchphrase } from ':modules/ai/helpers';
import { AiPayloadOptions, GenerateReviewReplyAdvancedSettingsPayload, RestaurantWithCategory } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { PreviousReviewsAnalysisService } from ':modules/ai/services/previous-reviews-analysis/previous-reviews-analysis.service';
import { RestaurantAiSettings } from ':modules/restaurant-ai-settings/entities/restaurant-ai-settings.entity';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { TranslateCatchphraseAndSignatureService } from ':modules/translations/services/translate-catchphrase-and-signature.service';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';

@singleton()
export class AnswerReviewPreviewUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _restaurantAiSettingsRepository: RestaurantAiSettingsRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _aiReviewsService: AiReviewsService<GenerateReviewReplyAdvancedSettingsPayload>,
        private readonly _translateCatchphraseAndSignatureService: TranslateCatchphraseAndSignatureService,
        private readonly _previousReviewsAnalysisService: PreviousReviewsAnalysisService
    ) {}

    async execute({
        restaurantId,
        restaurantAiSettings,
        sourceLanguage,
        text,
        lang,
        userId,
        reviewerName,
        retryCount = 1,
    }: {
        restaurantId: string;
        restaurantAiSettings: RestaurantAiSettings;
        sourceLanguage: ApplicationLanguage;
        text: string;
        reviewerName: string;
        lang: string;
        userId: string;
        retryCount?: number;
    }): Promise<string> {
        const aiInteraction = await this._aiInteractionsRepository.create({
            data: {
                type: AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                userId: userId ? toDbId(userId) : undefined,
            },
        });

        try {
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { _id: restaurantId },
                options: { lean: true, populate: [{ path: 'category' }] },
            });
            if (!restaurant) {
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found' });
            }
            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const restaurantAiSettingsWithTranslations = await this._getRestaurantAiSettingsWithTranslations({
                restaurantAiSettings,
                restaurantId,
                sourceLanguage,
                userId,
            });

            const payload = await this._computePayload({
                restaurantAiSettings: restaurantAiSettingsWithTranslations,
                text,
                lang,
                restaurant,
                reviewerName,
            });

            let completion: GenericAiServiceResponseType<AiTranslationsText | string> | undefined = undefined;
            let retry = 0;
            while (retry < retryCount) {
                try {
                    completion = await this._aiReviewsService.generateCompletion(payload);
                    break;
                } catch (error) {
                    retry++;
                    if (retry === retryCount) {
                        throw error;
                    }
                }
            }

            assert(completion);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS,
            });
            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                completion.aiInteractionDetails?.[0],
                restaurant._id
            );
            await this._aiInteractionsRepository.findOneAndUpdateOrFail({
                filter: { _id: aiInteraction._id },
                update: updatedAiInteraction,
                options: { new: true },
            });
            return completion?.aiResponse.toString();
        } catch (error: any) {
            logger.error('[AiUseCases] [answerReviewPreview] Error', { error, text, lang, restaurantAiSettings });
            await this._aiInteractionsRepository.findOneAndUpdateOrFail({
                filter: { _id: aiInteraction._id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _getRestaurantAiSettingsWithTranslations({
        restaurantAiSettings,
        restaurantId,
        sourceLanguage,
        userId,
    }: {
        restaurantAiSettings: RestaurantAiSettings;
        restaurantId: string;
        sourceLanguage: ApplicationLanguage;
        userId: string;
    }): Promise<RestaurantAiSettings> {
        const savedRestaurantAiSettings =
            await this._restaurantAiSettingsRepository.getRestaurantAiSettingsByRestaurantIdWithTranslations(restaurantId);
        const isCatchphraseTranslationValid =
            savedRestaurantAiSettings &&
            savedRestaurantAiSettings.reviewSettings?.catchphrase === restaurantAiSettings.reviewSettings.catchphrase;
        if (
            restaurantAiSettings.reviewSettings?.shouldTranslateCatchphrase &&
            !!restaurantAiSettings?.reviewSettings.catchphrase?.length &&
            !isCatchphraseTranslationValid
        ) {
            restaurantAiSettings.reviewSettings.catchphraseTranslation = await this._translateCatchphraseAndSignatureService.execute({
                type: AiInteractionType.INTRODUCTIVE_TRANSLATION,
                textToTranslate: restaurantAiSettings.reviewSettings.catchphrase,
                sourceLanguage,
                restaurantId,
                userId,
                currentRestaurantAiSettingsId: savedRestaurantAiSettings?.id,
            });
        } else if (savedRestaurantAiSettings) {
            restaurantAiSettings.reviewSettings.catchphraseTranslation = savedRestaurantAiSettings.reviewSettings?.catchphraseTranslation;
        }

        const shouldTranslateSignatures =
            restaurantAiSettings.reviewSettings?.shouldTranslateSignature &&
            !!restaurantAiSettings?.reviewSettings?.signatures?.filter(isNotNilOrEmpty)?.length;
        const areSignatureTranslationsValid =
            savedRestaurantAiSettings?.reviewSettings &&
            savedRestaurantAiSettings.reviewSettings.signatures?.length === restaurantAiSettings.reviewSettings?.signatures?.length &&
            restaurantAiSettings.reviewSettings.signatures?.every((signature) =>
                savedRestaurantAiSettings.reviewSettings?.signatures?.includes(signature)
            );
        if (shouldTranslateSignatures && !areSignatureTranslationsValid) {
            restaurantAiSettings.reviewSettings.signatureTranslations = await Promise.all(
                restaurantAiSettings.reviewSettings.signatures.filter(isNotNilOrEmpty)?.map((signature) =>
                    this._translateCatchphraseAndSignatureService.execute({
                        type: AiInteractionType.SIGNATURE_TRANSLATION,
                        textToTranslate: signature,
                        sourceLanguage,
                        restaurantId,
                        userId,
                        currentRestaurantAiSettingsId: savedRestaurantAiSettings?.id,
                    })
                )
            );
        } else if (savedRestaurantAiSettings) {
            restaurantAiSettings.reviewSettings.signatureTranslations = savedRestaurantAiSettings.reviewSettings.signatureTranslations;
        }
        return restaurantAiSettings;
    }

    private async _computePayload({
        restaurantAiSettings,
        text,
        lang,
        restaurant,
        reviewerName,
    }: {
        restaurantAiSettings: RestaurantAiSettings;
        text: string;
        lang: string;
        reviewerName: string;
        restaurant: RestaurantWithCategory;
    }): Promise<GenerateReviewReplyAdvancedSettingsPayload> {
        const review = this._createFakeReview({ restaurantId: restaurant._id, text, lang, reviewerName });
        const previousReviews = await this._previousReviewsAnalysisService.getReviewCommentsSample(
            review,
            AiPayloadOptions.previousReviewsSampleSize,
            lang
        );
        const previousMatchedReviewsAnalysis = await this._previousReviewsAnalysisService.analyzePreviousReviews({
            review,
            restaurantId: restaurant._id.toString(),
            previousReviews,
        });

        const matchedPreviousReviews = previousReviews.filter((previousReview) =>
            previousMatchedReviewsAnalysis.matchedReviewsIds.includes(previousReview.id)
        );

        const restaurantName = restaurantAiSettings ? restaurantAiSettings.restaurantName : restaurant.name;

        const {
            category: { categoryName },
        } = restaurant;
        const category = categoryName?.[lang] || categoryName?.backup || AiPayloadOptions.defaultBusinessCategoryName;

        const languageDisplayName = getApplicationLanguageDisplayName(lang, 'en') ?? lang;

        const { catchphrase, signature } = getSignatureAndCatchphrase({ restaurantAiSettings, language: lang });

        const customPrompt = (await this._isReviewsAiSettingsCustomPromptEnabled({ restaurantId: restaurant._id.toString() }))
            ? (restaurantAiSettings.reviewSettings.prompt ?? '')
            : '';

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS,
            restaurantData: {
                restaurantName: restaurantName,
                restaurantCategory: category,
                restaurantCity: restaurant.address?.locality ?? '',
                language: languageDisplayName,
                previousReviewsComments: matchedPreviousReviews.map((previousReview) => ({
                    reviewText: previousReview.reviewText ?? '',
                    responseText: previousReview.responseText,
                })),
                platform: review.key,
                reviewRating: review.rating,
                reviewerName: review.reviewer?.displayName ?? '',
                review: review.text ?? '',
                badwords: restaurantAiSettings.reviewSettings.forbiddenWords,
                signature,
                shouldTranslateSignature: restaurantAiSettings.reviewSettings.shouldTranslateSignature,
                userAddress: restaurantAiSettings.reviewSettings.customerNaming,
                tone: restaurantAiSettings.reviewSettings.replyTone,
                introductiveSentence: catchphrase,
                shouldTranslateIntroductiveSentence: restaurantAiSettings.reviewSettings.shouldTranslateCatchphrase,
                customPrompt,
                responseStyle: previousMatchedReviewsAnalysis.responseStyle,
                reviewerNameValidation: previousMatchedReviewsAnalysis.reviewerNameValidation,
            },
            model: restaurantAiSettings.reviewSettings.model,
            modelProvider: restaurantAiSettings.reviewSettings.modelProvider,
        };
    }

    private _createFakeReview({
        restaurantId,
        text,
        lang,
        reviewerName,
    }: {
        restaurantId: DbId;
        text: string;
        lang: string;
        reviewerName: string;
    }): IReview {
        return {
            restaurantId,
            rating: 5,
            key: PlatformKey.GMB,
            text,
            lang,
            reviewer: {
                displayName: reviewerName,
            },
            _id: newDbId(),
            platformId: newDbId(),
            comments: [],
            socialId: 'FAKE_REVIEW_PREVIEW_SOCIAL_ID',
            createdAt: new Date(),
            socialCreatedAt: new Date(),
            socialSortDate: new Date(),
            updatedAt: new Date(),
            archived: false,
            platformPresenceStatus: PlatformPresenceStatus.FOUND,
        };
    }

    private async _isReviewsAiSettingsCustomPromptEnabled({ restaurantId }: { restaurantId: string }): Promise<boolean> {
        const isEnabledForRestaurant = await isFeatureAvailableForRestaurant({
            restaurantId,
            featureName: 'release-reviews-ai-settings-custom-prompt',
        });
        return isEnabledForRestaurant;
    }
}
