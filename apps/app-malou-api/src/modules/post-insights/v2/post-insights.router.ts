import { Router } from 'express';
import { singleton } from 'tsyringe';

import { PostInsightsController } from ':modules/post-insights/v2/post-insights.controller';

@singleton()
export default class PostInsightsRouterV2 {
    private readonly _prefix = '/post-insights/v2';

    constructor(private readonly _postsInsightsController: PostInsightsController) {}

    init(_router: Router): void {
        // Define routes for post insights here
    }
}
