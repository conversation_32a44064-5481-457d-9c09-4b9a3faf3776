import { PostInsightEntityType } from '@malou-io/package-utils';

import { BasePostInsight, BasePostInsightProps } from ':modules/post-insights/v2/entities/base-post-insight.entity';

export interface PostInsightData {
    impressions: number;
    reach: number | null;
    plays: number | null;
    likes: number;
    comments: number;
    shares: number;
    saved: number | null;
}

export type PostInsightProps = BasePostInsightProps & {
    data: PostInsightData;
};

export class RegularPostInsight extends BasePostInsight {
    data: PostInsightData;

    constructor(props: PostInsightProps) {
        super({ ...props, entityType: PostInsightEntityType.POST });
        this.data = props.data;
    }
}
