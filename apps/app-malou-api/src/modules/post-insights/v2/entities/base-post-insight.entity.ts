import { ISocialAttachment } from '@malou-io/package-models';
import { PostInsightEntityType, PostType } from '@malou-io/package-utils';

export type BasePostInsightProps = {
    id: string;
    platformKey: string;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;
    lastFetchedAt: Date;
    postSocialCreatedAt: Date;
    post: ISourcePost;
    createdAt?: Date;
    updatedAt?: Date;
};
export interface ISourcePost {
    postType: PostType;
    socialLink?: string;
    attachments?: ISocialAttachment[];
    socialAttachments?: ISocialAttachment[];
}

export class BasePostInsight {
    id: string;
    platformKey: string;
    socialId: string;
    entityType: PostInsightEntityType;
    platformSocialId: string;
    lastFetchedAt: Date;
    postSocialCreatedAt: Date;
    post: ISourcePost;
    createdAt?: Date;
    updatedAt?: Date;

    constructor(props: BasePostInsightProps) {
        this.id = props.id;
        this.platformKey = props.platformKey;
        this.socialId = props.socialId;
        this.entityType = props.entityType;
        this.platformSocialId = props.platformSocialId;
        this.lastFetchedAt = props.lastFetchedAt;
        this.postSocialCreatedAt = props.postSocialCreatedAt;
        this.post = props.post;
        this.createdAt = props.createdAt;
        this.updatedAt = props.updatedAt;
    }

    shouldBeRefetched(): boolean {
        // TODO Implement logic to determine if the insight should be refetched
        return false;
    }
}
