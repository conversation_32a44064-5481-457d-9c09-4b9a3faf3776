import { PostInsightEntityType } from '@malou-io/package-utils';

import { BasePostInsight, BasePostInsightProps } from ':modules/post-insights/v2/entities/base-post-insight.entity';

export interface StoryInsightData {
    impressions: number;
    reach: number;
    taps_forward: number;
    taps_back: number;
    replies: number;
    taps_exits: number;
}

export type StoryInsightProps = BasePostInsightProps & {
    data: StoryInsightData;
};

export class StoryInsightEntity extends BasePostInsight {
    data: StoryInsightData;

    constructor(props: StoryInsightProps) {
        super({ ...props, entityType: PostInsightEntityType.STORY });
        this.data = props.data;
    }
}
