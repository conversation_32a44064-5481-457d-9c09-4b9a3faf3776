import { singleton } from 'tsyringe';

import { PlatformKey } from '@malou-io/package-utils';

@singleton()
export class GetRestaurantPostInsightsUseCase {
    constructor() {}

    async execute({
        _restaurantId,
        _startDate,
        _endDate,
        _platformKeys,
    }: {
        _restaurantId: string;
        _startDate: Date;
        _endDate: Date;
        _platformKeys: PlatformKey[];
    }): Promise<any> {
        // This is a placeholder for the actual implementation.
    }
}
