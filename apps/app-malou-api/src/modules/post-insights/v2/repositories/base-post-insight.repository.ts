import { singleton } from 'tsyringe';

import { EntityRepository, IPostInsightV2, PostInsightModelV2 } from '@malou-io/package-models';

export const DEFAULT_POST_POPULATE_OPTIONS = [
    {
        path: 'post' as const,
        select: {
            postType: 1,
            attachments: 1,
            socialAttachments: 1,
            socialLink: 1,
        },
        populate: [
            {
                path: 'attachments' as const,
                select: { type: 1, urls: 1, thumbnailUrl: 1 },
                options: { lean: true },
            },
        ] as any,
        options: { lean: true },
    },
];

@singleton()
export class BasePostInsightRepository extends EntityRepository<IPostInsightV2> {
    constructor() {
        super(PostInsightModelV2);
    }
}
