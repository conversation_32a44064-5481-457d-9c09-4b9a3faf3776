import { partition } from 'lodash';
import { singleton } from 'tsyringe';

import { postsUpdateTexts } from '@malou-io/package-utils';

import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import { FacebookPostInsightsMapper } from ':modules/post-insights/v2/platforms/facebook/facebook-post-insights.mapper';
import { InstagramPostInsightMapper } from ':modules/post-insights/v2/platforms/instagram/instagram-post-insights.mapper';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { IgMediaProductType, IgPostData } from ':modules/posts/platforms/instagram/instagram-post.interface';

@singleton()
export class UpsertPlatformPostInsightsService {
    constructor(private readonly _postInsightRepository: PostInsightRepository) {}

    async upsertFbRegularPostInsights(params: { posts: FbPostData[]; platformSocialId: string }): Promise<void> {
        const { posts, platformSocialId } = params;
        const mappedPost = posts
            .filter((p) => p.status_type !== 'mobile_status_update' && p.status_type !== 'shared_story' && p.is_published !== false)
            .filter((p) => !p.story || !postsUpdateTexts.FACEBOOK.filter((updateRegExp) => p.story?.match(updateRegExp))?.length)
            .map((post) => FacebookPostInsightsMapper.mapToMalouRegularPostInsight({ post, platformSocialId }));

        await this._postInsightRepository.upsertMany(mappedPost);
    }

    async upsertFbReelInsights(params: {
        reels: FacebookApiTypes.Reels.GetReelWithInsightsResponse[];
        platformSocialId: string;
    }): Promise<void> {
        const { reels, platformSocialId } = params;
        const mappedReels = reels.map((reel) => FacebookPostInsightsMapper.mapToMalouReelInsight({ reel, platformSocialId }));
        await this._postInsightRepository.upsertMany(mappedReels);
    }

    async upsertIgPostInsights(params: { posts: IgPostData[]; platformSocialId: string }): Promise<void> {
        const { posts, platformSocialId } = params;
        const [reels, regularPosts] = partition(posts, (post) => post.media_product_type === IgMediaProductType.REELS);

        if (reels.length) {
            const mappedReels = reels.map((post) => InstagramPostInsightMapper.mapToMalouReelInsight({ post, platformSocialId }));
            await this._postInsightRepository.upsertMany(mappedReels);
        }

        if (regularPosts.length) {
            const mappedPosts = regularPosts.map((post) =>
                InstagramPostInsightMapper.mapToMalouRegularPostInsight({ post, platformSocialId })
            );

            await this._postInsightRepository.upsertMany(mappedPosts);
        }
    }
}
