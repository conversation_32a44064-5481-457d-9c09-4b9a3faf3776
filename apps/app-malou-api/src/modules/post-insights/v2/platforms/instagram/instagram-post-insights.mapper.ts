import assert from 'node:assert/strict';

import { PlatformKey, PostInsightEntityType } from '@malou-io/package-utils';

import { MappedPostInsight } from ':modules/post-insights/v2/platforms/interface';
import { IgPostData, IgPostInsightFbName } from ':modules/posts/platforms/instagram/instagram-post.interface';

export class InstagramPostInsightMapper {
    static mapToMalouRegularPostInsight({ post, platformSocialId }: { post: IgPostData; platformSocialId: string }): MappedPostInsight {
        return this._mapToMalouPostInsight({
            post,
            platformSocialId,
            entityType: PostInsightEntityType.POST,
        });
    }

    static mapToMalouReelInsight({ post, platformSocialId }: { post: IgPostData; platformSocialId: string }): MappedPostInsight {
        return this._mapToMalouPostInsight({
            post,
            platformSocialId,
            entityType: PostInsightEntityType.REEL,
        });
    }

    private static _mapToMalouPostInsight({
        post,
        platformSocialId,
        entityType,
    }: {
        post: IgPostData;
        platformSocialId: string;
        entityType: PostInsightEntityType;
    }): MappedPostInsight {
        assert(post.id, '[InstagramPostInsightMapper] Missing id on post');
        assert(post.timestamp, '[InstagramPostInsightMapper] Missing timestamp on post');

        const _getMetricValue = (insights: IgPostData['insights'] | undefined, metric: IgPostInsightFbName): number | undefined =>
            insights?.data.find((d) => d.name === metric)?.values[0].value;

        const insights: IgPostData['insights'] | undefined = post.insights;
        return {
            socialId: post.id,
            platformKey: PlatformKey.INSTAGRAM,
            entityType,
            postSocialCreatedAt: new Date(post.timestamp),
            platformSocialId,
            lastFetchedAt: new Date(),
            data: {
                impressions:
                    _getMetricValue(insights, IgPostInsightFbName.VIEWS) || // Very important to keep || instead of ?? here, because instagram will return 0 for old posts for this metric
                    _getMetricValue(insights, IgPostInsightFbName.IMPRESSIONS) ||
                    0,
                reach: _getMetricValue(insights, IgPostInsightFbName.REACH) || 0,
                plays:
                    _getMetricValue(insights, IgPostInsightFbName.VIEWS) || // Very important to keep || instead of ?? here, because instagram will return 0 for old posts for this metric
                    _getMetricValue(insights, IgPostInsightFbName.PLAYS) ||
                    _getMetricValue(insights, IgPostInsightFbName.VIDEO_VIEWS) ||
                    (entityType === PostInsightEntityType.REEL ? 0 : null), // Plays is not available for regular posts
                likes: _getMetricValue(insights, IgPostInsightFbName.LIKES)
                    ? (_getMetricValue(insights, IgPostInsightFbName.LIKES) ?? 0)
                    : (post?.like_count ?? 0),
                comments: post.comments_count ?? 0,
                saved: _getMetricValue(insights, IgPostInsightFbName.SAVED) || 0,
                shares: _getMetricValue(insights, IgPostInsightFbName.SHARES) || 0,
            },
        };
    }
}
