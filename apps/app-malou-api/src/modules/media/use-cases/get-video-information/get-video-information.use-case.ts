import { inject, singleton } from 'tsyringe';

import {
    MultimediaStreamsInformationService,
    VideoStreamInformation,
} from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service';
import { MultimediaStreamsInformationServiceFfprobeAdapter } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service-ffprobe-adapter';

@singleton()
export class GetVideoInformationUseCase {
    constructor(
        @inject(MultimediaStreamsInformationServiceFfprobeAdapter)
        private readonly _multimediaStreamsInformationService: MultimediaStreamsInformationService
    ) {}

    async execute(url: string): Promise<VideoStreamInformation> {
        const videoInformation = await this._multimediaStreamsInformationService.getVideoStreamInformation(url);
        return videoInformation;
    }
}
