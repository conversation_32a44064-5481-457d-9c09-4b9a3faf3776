import { err, ok, Result } from 'neverthrow';
import { randomUUID } from 'node:crypto';
import { inject, singleton } from 'tsyringe';

import { IMedia, toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import { MultimediaStreamsInformationService } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service';
import { MultimediaStreamsInformationServiceFfprobeAdapter } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service-ffprobe-adapter';
import { MediasRepository } from ':modules/media/medias.repository';
import { UploadVideoService } from ':modules/media/use-cases/upload-media-v2/services/upload-video.service';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

enum GenerateNormalizedVideoUseCaseError {
    MEDIA_NOT_FOUND = 'MEDIA_NOT_FOUND',
    ERR_NORMALIZING_VIDEO = 'ERR_NORMALIZING_VIDEO',
}

@singleton()
export class GenerateNormalizedVideoUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _uploadVideoService: UploadVideoService,
        @inject(AwsS3DistantStorageService) private readonly _distantStorageService: DistantStorageService,
        @inject(MultimediaStreamsInformationServiceFfprobeAdapter)
        private readonly _multimediaStreamsInformationService: MultimediaStreamsInformationService
    ) {}
    async execute(mediaId: string): Promise<Result<void, GenerateNormalizedVideoUseCaseError>> {
        logger.info('[GenerateNormalizedVideoUseCase] Start', { mediaId });
        const media = await this._mediasRepository.findById(mediaId);
        if (!media) {
            logger.info('[GenerateNormalizedVideoUseCase] Media not found', { mediaId });
            return err(GenerateNormalizedVideoUseCaseError.MEDIA_NOT_FOUND);
        }
        const uuid = randomUUID();
        // At this point, normalized <=> original in the media document
        if (!media.storedObjects) {
            throw new Error('storedObjects not defined');
        }
        const s3SourceKey = media.storedObjects.normalized.key;
        const publicSourceUrl = await this._distantStorageService.getPublicAccessibleUrl(s3SourceKey);
        const isHdrVideo = await this._multimediaStreamsInformationService.isHdrVideo(publicSourceUrl);
        logger.info('[GenerateNormalizedVideoUseCase] isHdr', { isHdrVideo });
        const res = await this._uploadVideoService.normalizeVideo({ uuid, s3SourceKey, isHdrVideo });
        if (res.isErr()) {
            logger.info('[GenerateNormalizedVideoUseCase] Error normalizing video', { mediaId });
            return err(GenerateNormalizedVideoUseCaseError.ERR_NORMALIZING_VIDEO);
        }
        const value = res.value;
        const mediaUpdate: Partial<IMedia> = {
            dimensions: {
                ...media.dimensions,
                normalized: { width: value.normalizedWidth, height: value.normalizedHeight },
            },
            urls: {
                original: value.normalizedObject.publicUrl,
            },
            storedObjects: {
                ...media.storedObjects,
                normalized: value.normalizedObject,
            },
            isVideoNormalized: true,
        };
        await this._mediasRepository.updateOne({
            filter: { _id: toDbId(mediaId) },
            update: mediaUpdate,
            options: { lean: true, new: true },
        });
        logger.info('[GenerateNormalizedVideoUseCase] Success', { mediaId });
        return ok(undefined);
    }
}
