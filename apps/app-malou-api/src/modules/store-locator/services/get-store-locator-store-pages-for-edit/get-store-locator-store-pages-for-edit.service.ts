import { singleton } from 'tsyringe';

import { GetStoreLocatorDraftPagesDto } from '@malou-io/package-dto';
import { ReadPreferenceMode } from '@malou-io/package-models';
import { isNotNil, StoreLocatorPageStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { FetchStoreLocatorStoreService } from ':modules/store-locator/services/fetch-store-data/fetch-store-data.service';
import { GetStoreLocatorMapService } from ':modules/store-locator/services/get-store-locator-map/get-store-locator-map.service';
import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';
import { StoreLocatorRestaurantPageRepository } from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class GetStoreLocatorStorePagesForEditService {
    constructor(
        private readonly _getStoreLocatorMapService: GetStoreLocatorMapService,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _fetchStoreLocatorStoreService: FetchStoreLocatorStoreService
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorDraftPagesDto> {
        const storeLocatorOrganizationConfig =
            await this._storeLocatorOrganizationConfigRepository.getOrganizationConfiguration(organizationId);

        const restaurantsPages = await this._getStoreLocatorStores(storeLocatorOrganizationConfig);

        return {
            restaurantsPages,
        };
    }

    /**
     * We use the backup blocks to enhance the performance of fetching store locator stores for edit,
     * (Head, Reviews, Social Networks) blocks are not used in the edit mode,
     */
    private async _getStoreLocatorStores(
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration
    ): Promise<GetStoreLocatorDraftPagesDto['restaurantsPages']> {
        try {
            const storeLocatorRestaurantPages = await this._storeLocatorRestaurantPageRepository.find({
                filter: {
                    organizationId: storeLocatorOrganizationConfig.organizationId,
                    status: StoreLocatorPageStatus.DRAFT,
                },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
            });

            const restaurantPages = await Promise.all(
                storeLocatorRestaurantPages.map((storeLocatorRestaurantPage) =>
                    this._fetchStoreLocatorStoreService.execute({
                        restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
                        storeLocatorRestaurantPage,
                        storeLocatorOrganizationConfig,
                        isForEdit: true,
                    })
                )
            );

            return restaurantPages.map(({ data }) => data).filter(isNotNil);
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to get stores for edit', { err });
            throw err;
        }
    }
}
