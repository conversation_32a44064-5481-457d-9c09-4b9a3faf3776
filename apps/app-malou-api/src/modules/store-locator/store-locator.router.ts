import { Router } from 'express';
import { singleton } from 'tsyringe';

import { apiKeyAuthorize } from ':modules/api-keys/middlewares';
import { getPagesFromCacheMiddleware } from ':modules/store-locator/middlewares/get-pages-cache.middleware';
import StoreLocatorController from ':modules/store-locator/store-locator.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class StoreLocatorRouter {
    constructor(private _storeLocatorController: StoreLocatorController) {}

    init(router: Router): void {
        router.get(
            '/store-locator/:organizationId/pages',
            apiKeyAuthorize,
            (req: any, res, next) => getPagesFromCacheMiddleware(req, res, next),
            (req: any, res, next) => this._storeLocatorController.handleGetPages(req, res, next)
        );

        router.get('/store-locator/:organizationId/configuration', apiKeyAuthorize, (req: any, res, next) =>
            this._storeLocatorController.handleGetOrganizationConfiguration(req, res, next)
        );

        router.get('/store-locator/:organizationId/pages', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGetPagesForEdit(req, res, next)
        );

        router.get('/store-locator/:organizationId/centralization', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGetStoreLocatorCentralizationPages(req, res, next)
        );

        router.get('/store-locator/:organizationId/organization-configuration', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGetStoreLocatorOrganizationConfiguration(req, res, next)
        );

        router.get('/store-locator/:organizationId/check-for-restaurant-pages', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleCheckForStoreLocatorRestaurantPages(req, res, next)
        );

        router.put('/store-locator/:organizationId/organization-configuration/ai-settings', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateOrganizationConfigurationAiSettings(req, res, next)
        );

        router.put('/store-locator/:organizationId/all-pages', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateRestaurantPages(req, res, next)
        );

        router.post('/store-locator/:organizationId/generate-page-content', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleGeneratePageContent(req, res, next)
        );

        router.put('/store-locator/:organizationId/organization-configuration/store-pages', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateOrganizationConfigurationStorePages(req, res, next)
        );

        router.put('/store-locator/:organizationId/organization-configuration/languages', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleUpdateOrganizationConfigurationLanguages(req, res, next)
        );

        router.get('/store-locator/:organizationId/organization-jobs', (req: any, res, next) =>
            this._storeLocatorController.handleGetOrganizationJobs(req, res, next)
        );

        router.get('/store-locator/:organizationId/generate/start', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleStartStoreLocatorPagesGeneration(req, res, next)
        );

        router.get('/store-locator/:organizationId/generate/watch/:jobId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleWatchStoreLocatorPagesGeneration(req, res, next)
        );

        router.get('/store-locator/:organizationId/publish/start', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleStartStoreLocatorPublication(req, res, next)
        );

        router.get('/store-locator/:organizationId/send-subscription-request', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleStoreLocatorSendSubscriptionRequest(req, res, next)
        );

        router.get('/store-locator/:organizationId/publish/watch/:jobId', authorize(), (req: any, res, next) =>
            this._storeLocatorController.handleWatchStoreLocatorStorePublication(req, res, next)
        );

        // Webhooks triggered by Github Actions
        router.post('/store-locator/deployment/update-status', apiKeyAuthorize, (req: any, res, next) =>
            this._storeLocatorController.handleUpdateDeploymentStatus(req, res, next)
        );
    }
}
