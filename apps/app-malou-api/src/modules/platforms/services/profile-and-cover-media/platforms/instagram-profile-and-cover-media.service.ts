import { singleton } from 'tsyringe';

import { MediaCategory, PlatformKey } from '@malou-io/package-utils';

import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';

@singleton()
export class InstagramProfileAndCoverMediaService {
    async getProfileAndCoverMedia({
        credentialId,
        socialId,
    }: {
        credentialId: string;
        socialId: string;
    }): Promise<{ url: string | undefined; category: MediaCategory; source: PlatformKey }[]> {
        const account = await facebookCredentialsUseCases.getAccount(credentialId, socialId);
        return [
            {
                url: account.instagram_business_account?.profile_picture_url ?? account.picture?.data?.url,
                category: MediaCategory.PROFILE,
                source: PlatformKey.INSTAGRAM,
            },
        ];
    }
}
