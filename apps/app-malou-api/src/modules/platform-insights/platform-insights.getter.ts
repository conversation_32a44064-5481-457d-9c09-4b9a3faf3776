import { container } from 'tsyringe';

import { Malou<PERSON>rrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightUseCase } from ':modules/platform-insights/platform-insights.types';
import { FacebookPlatformInsights } from ':modules/platform-insights/platforms/facebook/use-cases';
import { FoursquarePlatformInsights } from ':modules/platform-insights/platforms/foursquare/use-cases';
import { GmbPlatformInsights } from ':modules/platform-insights/platforms/gmb/use-cases';
import { InstagramPlatformInsights } from ':modules/platform-insights/platforms/instagram/use-cases';
import { LaFourchettePlatformInsights } from ':modules/platform-insights/platforms/lafourchette/use-cases';
import { OpenTablePlatformInsights } from ':modules/platform-insights/platforms/opentable/use-cases';
import { ResyPlatformInsights } from ':modules/platform-insights/platforms/resy/use-cases';
import { TiktokPlatformInsights } from ':modules/platform-insights/platforms/tiktok/use-cases';
import { TripadvisorPlatformInsights } from ':modules/platform-insights/platforms/tripadvisor/use-cases';
import { YelpPlatformInsights } from ':modules/platform-insights/platforms/yelp/use-cases';

export const getPlatformInsights = function (key: PlatformKey): PlatformInsightUseCase | null {
    switch (key) {
        case PlatformKey.INSTAGRAM:
            return container.resolve(InstagramPlatformInsights);
        case PlatformKey.FACEBOOK:
            return container.resolve(FacebookPlatformInsights);
        case PlatformKey.GMB:
            return container.resolve(GmbPlatformInsights);
        case PlatformKey.YELP:
            return container.resolve(YelpPlatformInsights);
        case PlatformKey.LAFOURCHETTE:
            return container.resolve(LaFourchettePlatformInsights);
        case PlatformKey.TRIPADVISOR:
            return container.resolve(TripadvisorPlatformInsights);
        case PlatformKey.FOURSQUARE:
            return container.resolve(FoursquarePlatformInsights);
        case PlatformKey.OPENTABLE:
            return container.resolve(OpenTablePlatformInsights);
        case PlatformKey.RESY:
            return container.resolve(ResyPlatformInsights);
        case PlatformKey.TIKTOK:
            return container.resolve(TiktokPlatformInsights);
        case PlatformKey.ZENCHEF:
        case PlatformKey.DELIVEROO:
        case PlatformKey.SEVENROOMS:
        case PlatformKey.DOORDASH:
            return null;
        default:
            throw new MalouError(MalouErrorCode.PLATFORM_INVALID_KEY, { metadata: { key } });
    }
};
