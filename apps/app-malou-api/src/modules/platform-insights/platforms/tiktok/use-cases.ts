import { groupBy, sumBy } from 'lodash';
import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';

import { DbId, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    AggregationTimeScale,
    errorReplacer,
    isBetween,
    isNotNil,
    MalouErrorCode,
    MalouMetric,
    PlatformKey,
    StoredInDBInsightsMetric,
    TimeInMilliseconds,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightFiltersApi } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import { InsightsAggregator } from ':modules/platform-insights/platform-insights.aggregators';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import {
    DailyValue,
    MetricToDataValues,
    PlatformInsightUseCase,
    TimeScaleToMetricToDataValues,
} from ':modules/platform-insights/platform-insights.types';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { TiktokPostData } from ':modules/posts/platforms/tiktok/tiktok-post.interface';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';

@autoInjectable()
export class TiktokPlatformInsights implements PlatformInsightUseCase {
    constructor(
        private _platformsRepository: PlatformsRepository,
        private _tiktokProvider: TiktokProvider,
        private _callTiktokApiService: CallTiktokApiService,
        private _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    getInsightsAggregated = async (
        restaurantId: string,
        metrics: MalouMetric[],
        aggregators: AggregationTimeScale[],
        filters: PlatformInsightFiltersApi
    ): Promise<TimeScaleToMetricToDataValues> => {
        const access = await this._getPlatformAccess(toDbId(restaurantId));
        if (access.error) {
            return access;
        }
        const credentialId = access?.credentialId?.toString();
        const pageId = access?.pageId;
        if (!credentialId || !pageId) {
            return {
                error: true,
                message: `${MalouErrorCode.PLATFORM_MISSING_PERMISSIONS} for restaurant ${restaurantId} and platform ${PlatformKey.TIKTOK}`,
            };
        }

        let insightsByDay: MetricToDataValues<DailyValue> = {};

        insightsByDay = await this._getInsightsByDay(credentialId, pageId, metrics, filters);

        if ('error' in insightsByDay) {
            return insightsByDay;
        }
        const { startDate, endDate } = filters;
        return new InsightsAggregator().aggregateInsights(insightsByDay, aggregators, startDate, endDate);
    };

    private _getInsightsByDay = async (
        credentialId: string,
        pageId: string,
        metrics: MalouMetric[],
        tiktokFilters: PlatformInsightFiltersApi
    ): Promise<MetricToDataValues<DailyValue>> => {
        const { startDate, endDate } = tiktokFilters;

        const insightsByDay: MetricToDataValues<DailyValue> = {};

        if (metrics.includes(MalouMetric.FOLLOWERS)) {
            const followersData = await this._getFollowersByDay(pageId, startDate, endDate);
            if (followersData.length) {
                insightsByDay[MalouMetric.FOLLOWERS] = followersData;
            }
        }

        const remainingMetrics = metrics.filter((metric) => metric !== MalouMetric.FOLLOWERS);

        if (remainingMetrics.length === 0) {
            return insightsByDay;
        }
        const {
            data: { videos },
        } = await this._callTiktokApiService.execute({
            credentialId: credentialId.toString(),
            method: this._tiktokProvider.getAllVideos,
            args: {},
        });

        const filteredVideos = videos.filter((post) =>
            isBetween(
                DateTime.fromMillis(post.create_time * TimeInMilliseconds.SECOND)
                    .startOf('day')
                    .toJSDate(),
                startDate,
                endDate
            )
        );

        const promises = metrics
            .filter((metric) => metric !== MalouMetric.FOLLOWERS)
            .map(async (metric) => {
                let data: DailyValue[] = [];
                switch (metric) {
                    case MalouMetric.POSTS:
                        data = this._getPosts(filteredVideos);
                        break;
                    case MalouMetric.ENGAGEMENTS:
                        data = this._getPostsEngagementsByDay(filteredVideos);
                        break;
                    case MalouMetric.IMPRESSIONS:
                        data = this._getImpressionsByDay(filteredVideos);
                        break;
                    default:
                        data = [];
                        break;
                }
                return { metric, data };
            });

        const results = await Promise.all(promises);

        results.forEach((result) => {
            insightsByDay[result.metric] = result.data;
        });

        if (!Object.keys(insightsByDay).length) {
            return { error: true, message: MalouErrorCode.INSIGHTS_NOT_FOUND };
        }

        return insightsByDay;
    };

    private _getPlatformAccess = async (restaurantId: DbId) => {
        try {
            const platform = await this._platformsRepository.findOne({
                filter: { restaurantId, key: PlatformKey.TIKTOK },
                options: { lean: true },
            });
            let credentialId: DbId | null = null;
            let pageId: string | null = null;
            if (platform && platform?.credentials?.length) {
                credentialId = platform.credentials[0] ?? null;
            }
            if (platform?.socialId) {
                pageId = platform.socialId;
            }
            if (!credentialId) {
                return {
                    error: true,
                    message: MalouErrorCode.PLATFORM_MISSING_PERMISSIONS,
                    metadata: { restaurantId, platformKey: PlatformKey.TIKTOK },
                };
            }
            return { credentialId, pageId };
        } catch (err) {
            logger.warn('[ERROR_TIKTOK_CREDENTIALS]', err);
            return { error: true, message: JSON.stringify(err, errorReplacer) };
        }
    };

    private _getFollowersByDay = async (pageId: string, startDate: Date, endDate: Date): Promise<DailyValue[]> => {
        const followersInsights = await this._platformInsightsRepository.find({
            filter: {
                metric: StoredInDBInsightsMetric.FOLLOWERS,
                socialId: pageId,
                platformKey: PlatformKey.TIKTOK,
                date: { $gte: startDate, $lte: endDate },
            },
            options: { sort: { createdAt: -1 }, lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
        return followersInsights.map((f) => (f.value ? { date: new Date(f.year, f.month, f.day), value: f.value } : null)).filter(isNotNil);
    };

    private _getPosts = (videos: TiktokPostData[]): DailyValue[] => {
        return videos.map((post) => ({
            date: DateTime.fromMillis(post.create_time * TimeInMilliseconds.SECOND)
                .startOf('day')
                .toJSDate(),
            value: 1,
        }));
    };

    private _getImpressionsByDay = (videos: TiktokPostData[]): DailyValue[] => {
        const mappedVideos = videos.map((post) => ({
            day: DateTime.fromMillis(post.create_time * TimeInMilliseconds.SECOND)
                .startOf('day')
                .toJSDate(),
            impressions: post.view_count ?? 0,
        }));
        const videosByDate = groupBy(mappedVideos, 'day');
        return Object.values(videosByDate).map((vids) => ({
            date: vids[0].day,
            value: sumBy(vids, 'impressions'),
        }));
    };

    private _getPostsEngagementsByDay = (videos: TiktokPostData[]): DailyValue[] => {
        return videos.map((post) => ({
            date: DateTime.fromMillis(post.create_time * TimeInMilliseconds.SECOND)
                .startOf('day')
                .toJSDate(),
            value: post.like_count + post.comment_count + post.share_count,
        }));
    };

    insertTodayFollowers = async (restaurantId: string) => {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.TIKTOK);
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    restaurantId,
                    platformKey: PlatformKey.TIKTOK,
                },
            });
        }
        const { credentials, socialId } = platform;
        const credentialId = credentials?.[0] ?? null;
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_MISSING_PERMISSIONS, {
                message: '[TIKTOK_CREDENTIAL] Need credentialId',
                metadata: {
                    restaurantId,
                    platformKey: PlatformKey.TIKTOK,
                },
            });
        }

        const {
            data: { user: userInfo },
        } = await this._callTiktokApiService.execute({
            credentialId: credentialId.toString(),
            method: this._tiktokProvider.getUserInfo,
            args: {
                fields: ['follower_count'],
            },
        });

        const followersCount = userInfo.follower_count;

        const today = DateTime.local();
        const { year, month, day } = today;
        const filter = {
            platformKey: PlatformKey.TIKTOK,
            socialId,
            metric: StoredInDBInsightsMetric.FOLLOWERS,
            year,
            month: month - 1,
            day,
        };
        const insight = {
            ...filter,
            value: followersCount,
            date: today.toJSDate(),
        };
        return this._platformInsightsRepository.upsert({ filter, update: insight });
    };

    async fetchTodayRating(): Promise<undefined> {
        return;
    }
}
