import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { autoInjectable } from 'tsyringe';

import { DbId, ID, ReadPreferenceMode } from '@malou-io/package-models';
import {
    AggregationTimeScale,
    errorReplacer,
    isBetween,
    isNotNil,
    MalouErrorCode,
    MalouMetric,
    PlatformKey,
    StoredInDBInsightsMetric,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightFiltersInstagram } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { InsightsAggregator } from ':modules/platform-insights/platform-insights.aggregators';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import {
    DailyValue,
    MetricToDataValues,
    PlatformI<PERSON>ightUseCase,
    TimeScaleToMetricToDataValues,
} from ':modules/platform-insights/platform-insights.types';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { InstagramPostMapper } from ':modules/posts/platforms/instagram/instagram-post-mapper';

@autoInjectable()
export class InstagramPlatformInsights implements PlatformInsightUseCase {
    constructor(
        private _platformsRepository: PlatformsRepository,
        private _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    getInsightsAggregated = async (
        restaurantId: string,
        metrics: MalouMetric[],
        aggregators: AggregationTimeScale[],
        filters: PlatformInsightFiltersInstagram
    ): Promise<TimeScaleToMetricToDataValues> => {
        const access = await this._getPlatformAccess(restaurantId);
        if (access.error) {
            return access;
        }
        const credentialId = access?.credentialId?.toString();
        const pageId = access?.pageId;
        if (!credentialId || !pageId) {
            return {
                error: true,
                message: `${MalouErrorCode.PLATFORM_MISSING_PERMISSIONS} for restaurant ${restaurantId} and platform ${PlatformKey.INSTAGRAM}`,
            };
        }

        let insightsByDay: MetricToDataValues<DailyValue> = {};

        insightsByDay = await this._getInsightsByDayV2(credentialId, pageId, metrics, filters);

        if ('error' in insightsByDay) {
            return insightsByDay;
        }
        const { startDate, endDate } = filters;
        return new InsightsAggregator().aggregateInsights(insightsByDay, aggregators, startDate, endDate);
    };

    private _getInsightsByDay = async (
        credentialId: string,
        pageId: string,
        metrics: MalouMetric[],
        instagramFilters: PlatformInsightFiltersInstagram
    ): Promise<MetricToDataValues<DailyValue>> => {
        const { startDate, endDate } = instagramFilters;
        const intervals = instagramFilters.buildQuery();

        const insightsByDay: MetricToDataValues<DailyValue> = {};

        if (metrics.includes(MalouMetric.IMPRESSIONS)) {
            try {
                insightsByDay[MalouMetric.IMPRESSIONS] = await this._getImpressionsByDay(credentialId, pageId, intervals);
            } catch (err) {
                logger.warn('[ERROR_IG_INSIGHTS_IMPRESSIONS]', err);
                return { error: true, message: JSON.stringify(err, errorReplacer) };
            }
        }

        if (metrics.includes(MalouMetric.ENGAGEMENTS)) {
            insightsByDay[MalouMetric.ENGAGEMENTS] = await this._getPostsEngagementsByDay(credentialId, pageId, intervals);
        }

        if (metrics.includes(MalouMetric.POSTS)) {
            insightsByDay[MalouMetric.POSTS] = await this._getPosts(credentialId, pageId, intervals);
        }

        if (metrics.includes(MalouMetric.FOLLOWERS)) {
            assert(startDate, 'startDate is undefined');
            assert(endDate, 'endDate is undefined');
            insightsByDay[MalouMetric.FOLLOWERS] = await this._getFollowersByDay(pageId, startDate, endDate);
        }

        if (!Object.keys(insightsByDay).length) {
            return { error: true, message: MalouErrorCode.INSIGHTS_NOT_FOUND };
        }

        return insightsByDay;
    };

    private _getInsightsByDayV2 = async (
        credentialId: string,
        pageId: string,
        metrics: MalouMetric[],
        instagramFilters: PlatformInsightFiltersInstagram
    ): Promise<MetricToDataValues<DailyValue>> => {
        const { startDate, endDate } = instagramFilters;
        const intervals = instagramFilters.buildQuery();

        const insightsByDay: MetricToDataValues<DailyValue> = {};

        if (metrics.includes(MalouMetric.IMPRESSIONS)) {
            try {
                insightsByDay[MalouMetric.IMPRESSIONS] = await this._getImpressionsByDay(credentialId, pageId, intervals);
            } catch (err) {
                logger.warn('[ERROR_IG_INSIGHTS_IMPRESSIONS]', err);
                return { error: true, message: JSON.stringify(err, errorReplacer) };
            }
        }

        const promises = metrics
            .filter((metric) => metric !== MalouMetric.IMPRESSIONS)
            .map(async (metric) => {
                let data: DailyValue[] = [];
                switch (metric) {
                    case MalouMetric.ENGAGEMENTS:
                        data = await this._getPostsEngagementsByDay(credentialId, pageId, intervals);
                        break;
                    case MalouMetric.POSTS:
                        data = await this._getPosts(credentialId, pageId, intervals);
                        break;
                    case MalouMetric.FOLLOWERS:
                        data = await this._getFollowersByDay(pageId, startDate, endDate);
                        break;
                    default:
                        data = [];
                        break;
                }
                return { metric, data };
            });

        const results = await Promise.all(promises);

        results.forEach((result) => {
            insightsByDay[result.metric] = result.data;
        });

        if (!Object.keys(insightsByDay).length) {
            return { error: true, message: MalouErrorCode.INSIGHTS_NOT_FOUND };
        }

        return insightsByDay;
    };

    private _getPlatformAccess = async (restaurantId: ID) => {
        try {
            const platform = await this._platformsRepository.findOne({
                filter: { restaurantId, key: PlatformKey.INSTAGRAM },
                options: { lean: true },
            });
            let credentialId: DbId | null = null;
            let pageId: string | null = null;
            if (platform && platform?.credentials?.length) {
                credentialId = platform.credentials[0] ?? null;
            }
            if (platform?.socialId) {
                pageId = platform.socialId;
            }
            if (!credentialId) {
                return {
                    error: true,
                    message: MalouErrorCode.PLATFORM_MISSING_PERMISSIONS,
                    metadata: { restaurantId, platformKey: PlatformKey.INSTAGRAM },
                };
            }
            return { credentialId, pageId };
        } catch (err) {
            logger.warn('[ERROR_IG_CREDENTIALS]', err);
            return { error: true, message: JSON.stringify(err, errorReplacer) };
        }
    };

    private _getFollowersByDay = async (pageId: ID, startDate: Date, endDate: Date): Promise<DailyValue[]> => {
        const followersInsights = await this._platformInsightsRepository.find({
            filter: {
                metric: StoredInDBInsightsMetric.FOLLOWERS,
                socialId: pageId,
                platformKey: PlatformKey.INSTAGRAM,
                date: { $gte: startDate, $lte: endDate },
            },
            options: { sort: { createdAt: -1 }, lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
        return followersInsights.map((f) => (f.value ? { date: new Date(f.year, f.month, f.day), value: f.value } : null)).filter(isNotNil);
    };

    private _getImpressionsByDay = async (
        credentialId: string,
        pageId: string,
        intervals: {
            since: string;
            until: string;
        }[]
    ): Promise<DailyValue[]> => {
        const startDate = intervals[0]?.since;
        const endDate = intervals[intervals.length - 1]?.until;
        if (!startDate || !endDate) {
            return [];
        }
        const postsWithInsights = await facebookCredentialsUseCases.igGetPagePostsWithInsights(
            credentialId,
            pageId,
            new Date(startDate),
            new Date(endDate)
        );
        const postsWithInsightsBetweenDates = postsWithInsights
            .filter((post) => !!post)
            .map((post) => new InstagramPostMapper().mapToInsightPost(post))
            .filter((post) => isBetween(post.createdAt, new Date(startDate), new Date(endDate)))
            .map((post) => new InstagramPostMapper().mapToPostInsightsStatistics(post));
        return postsWithInsightsBetweenDates.map((post) => ({
            date: post.createdAt,
            value: post.stats.impressions,
        }));
    };

    private _getPostsEngagementsByDay = async (credentialId: string, pageId: string, intervals): Promise<DailyValue[]> => {
        const { since: sinceBoundary } = intervals[0];
        const { until: untilBoundary } = intervals[intervals.length - 1];
        return facebookCredentialsUseCases.igGetPostsEngagements(credentialId, pageId, sinceBoundary, untilBoundary).then((posts) =>
            posts.data.map((post) => ({
                date: new Date(post.timestamp),
                value: post?.insights?.data[0]?.values[0].value,
            }))
        );
    };

    private _getPosts = async (credentialId: string, pageId: string, intervals): Promise<DailyValue[]> => {
        const { since: sinceBoundary } = intervals[0];
        const { until: untilBoundary } = intervals[intervals.length - 1];
        return facebookCredentialsUseCases.igGetPosts(credentialId, pageId, sinceBoundary, untilBoundary).then((posts) =>
            posts.data.map((post) => ({
                date: new Date(post.timestamp),
                value: 1,
            }))
        );
    };

    insertTodayFollowers = async (restaurantId: string) => {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.INSTAGRAM);
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    restaurantId,
                    platformKey: PlatformKey.INSTAGRAM,
                },
            });
        }
        const { key, socialId: pageId, credentials } = platform;
        const credentialId = credentials?.[0] ?? null;
        if (!credentialId || !pageId) {
            throw new MalouError(MalouErrorCode.PLATFORM_MISSING_PERMISSIONS, {
                message: '[INSTAGRAM_CREDENTIAL] Need credentialId and pageId',
                metadata: {
                    restaurantId,
                    platformKey: PlatformKey.INSTAGRAM,
                },
            });
        }
        const { followers_count: followersCount } = await facebookCredentialsUseCases.getFollowers(credentialId, pageId);
        const today = DateTime.local();
        const { year, month, day } = today;
        const filter = {
            platformKey: key,
            socialId: pageId,
            metric: StoredInDBInsightsMetric.FOLLOWERS,
            year,
            month: month - 1,
            day,
        };
        const insight = {
            ...filter,
            value: followersCount,
            date: today.toJSDate(),
        };
        return this._platformInsightsRepository.upsert({ filter, update: insight });
    };

    async fetchTodayRating(): Promise<undefined> {
        return;
    }
}
