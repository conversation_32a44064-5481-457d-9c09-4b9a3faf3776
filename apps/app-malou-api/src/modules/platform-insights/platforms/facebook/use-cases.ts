import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';

import { DbId, ID, ReadPreferenceMode } from '@malou-io/package-models';
import {
    AggregationTimeScale,
    errorReplacer,
    isNotNil,
    MalouErrorCode,
    MalouMetric,
    PlatformKey,
    StoredInDBInsightsMetric,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { tryForCredentials } from ':modules/credentials/credentials.helpers';
import { IFacebookDataWithPaging, IFacebookInsights } from ':modules/credentials/platforms/facebook/facebook.interface';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { InsightsAggregator } from ':modules/platform-insights/platform-insights.aggregators';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import {
    DailyValue,
    MetricToDataValues,
    PlatformInsightUseCase,
    TimeScaleToMetricToDataValues,
} from ':modules/platform-insights/platform-insights.types';
import PlatformsRepository from ':modules/platforms/platforms.repository';

interface ValueWithEndTime {
    value: number;
    end_time: Date;
}

interface ImpressionsAndEngagementsByDay {
    impressions: ValueWithEndTime[];
    engagements: ValueWithEndTime[];
}
@autoInjectable()
export class FacebookPlatformInsights implements PlatformInsightUseCase {
    constructor(
        private _platformsRepository: PlatformsRepository,
        private _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    getInsightsAggregated = async (
        restaurantId: string,
        metrics: MalouMetric[],
        aggregators: AggregationTimeScale[],
        filters
    ): Promise<TimeScaleToMetricToDataValues> => {
        const access = await this._getPlatformAccess(restaurantId);
        if (access.error) {
            return access;
        }
        const credentialId = access?.credentialId?.toString();
        const pageId = access?.pageId;

        let insightsByDay: MetricToDataValues<DailyValue> = {};

        if (!credentialId || !pageId) {
            throw new MalouError(MalouErrorCode.INVALID_DATA, {
                message: '[FB_CREDENTIAL] Need credentialId or pageId', // Frontend uses this message to handle errors
                metadata: {
                    platform: PlatformKey.FACEBOOK,
                },
            });
        }
        insightsByDay = await this._getInsightsByDayV2(credentialId, pageId, metrics, filters);

        if ('error' in insightsByDay) {
            return insightsByDay;
        }
        const { startDate, endDate } = filters;
        return new InsightsAggregator().aggregateInsights(insightsByDay, aggregators, startDate, endDate);
    };

    private _getInsightsByDay = async (
        credentialId: string,
        pageId: string,
        metrics: MalouMetric[],
        facebookFilters
    ): Promise<MetricToDataValues<DailyValue>> => {
        const { startDate, endDate } = facebookFilters;
        const intervals = facebookFilters.buildQuery();

        const insightsByDay: MetricToDataValues<DailyValue> = {};

        let impressionsAndEngagementsByDays: ImpressionsAndEngagementsByDay[] = [];
        if (metrics.includes(MalouMetric.IMPRESSIONS) || metrics.includes(MalouMetric.ENGAGEMENTS)) {
            try {
                impressionsAndEngagementsByDays = await this._getImpressionsAndEngagementsByDay(credentialId, pageId, intervals);
            } catch (err) {
                logger.warn('[ERROR_FB_INSIGHTS_IMPRESSIONS]', err);
                return { error: true, message: JSON.stringify(err, errorReplacer) };
            }
        }

        if (metrics.includes(MalouMetric.IMPRESSIONS)) {
            const impressionsInsights: DailyValue[] = impressionsAndEngagementsByDays
                .flatMap((impressionsAndEngagementsByDay: ImpressionsAndEngagementsByDay) => impressionsAndEngagementsByDay.impressions)
                .map((impressionsValue: ValueWithEndTime) => ({
                    value: impressionsValue.value,
                    date: new Date(impressionsValue.end_time),
                }));
            insightsByDay[MalouMetric.IMPRESSIONS] = impressionsInsights;
        }

        if (metrics.includes(MalouMetric.ENGAGEMENTS)) {
            const engagementsInsights: DailyValue[] = impressionsAndEngagementsByDays
                .flatMap((impressionsAndEngagementsByDay: ImpressionsAndEngagementsByDay) => impressionsAndEngagementsByDay.engagements)
                .map((impressionsValue: ValueWithEndTime) => ({
                    value: impressionsValue.value,
                    date: new Date(impressionsValue.end_time),
                }));
            insightsByDay[MalouMetric.ENGAGEMENTS] = engagementsInsights;
        }

        if (metrics.includes(MalouMetric.POSTS)) {
            insightsByDay[MalouMetric.POSTS] = await this._getPosts(credentialId, pageId, intervals);
        }

        if (metrics.includes(MalouMetric.FOLLOWERS)) {
            insightsByDay[MalouMetric.FOLLOWERS] = await this._getFollowersByDay(pageId, startDate, endDate);
        }

        if (!Object.keys(insightsByDay).length) {
            return { error: true, message: MalouErrorCode.INSIGHTS_NOT_FOUND };
        }
        return insightsByDay;
    };

    private _getInsightsByDayV2 = async (
        credentialId: string,
        pageId: string,
        metrics: MalouMetric[],
        facebookFilters
    ): Promise<MetricToDataValues<DailyValue>> => {
        const { startDate, endDate } = facebookFilters;
        const intervals = facebookFilters.buildQuery();

        const insightsByDay: MetricToDataValues<DailyValue> = {};

        let impressionsAndEngagementsByDays: ImpressionsAndEngagementsByDay[] = [];
        if (metrics.includes(MalouMetric.IMPRESSIONS) || metrics.includes(MalouMetric.ENGAGEMENTS)) {
            try {
                impressionsAndEngagementsByDays = await this._getImpressionsAndEngagementsByDay(credentialId, pageId, intervals);
            } catch (err) {
                logger.warn('[ERROR_FB_INSIGHTS_IMPRESSIONS]', err);
                return { error: true, message: JSON.stringify(err, errorReplacer) };
            }
        }

        const filteredMetrics = metrics.filter((metric) => metric !== MalouMetric.IMPRESSIONS && metric !== MalouMetric.ENGAGEMENTS);

        const promises: Promise<{
            metric: MalouMetric;
            data: DailyValue[];
        }>[] = filteredMetrics.map(async (metric) => {
            let data: DailyValue[] = [];
            switch (metric) {
                case MalouMetric.POSTS:
                    data = await this._getPosts(credentialId, pageId, intervals);
                    break;
                case MalouMetric.FOLLOWERS:
                    data = await this._getFollowersByDay(pageId, startDate, endDate);
                    break;
                default:
                    data = [];
                    break;
            }
            return { metric, data };
        });

        const results = await Promise.all(promises);

        if (metrics.includes(MalouMetric.IMPRESSIONS)) {
            const impressionsInsights: DailyValue[] = impressionsAndEngagementsByDays
                .flatMap((impressionsAndEngagementsByDay: ImpressionsAndEngagementsByDay) => impressionsAndEngagementsByDay.impressions)
                .map((impressionsValue: ValueWithEndTime) => ({
                    value: impressionsValue.value,
                    date: new Date(impressionsValue.end_time),
                }));
            insightsByDay[MalouMetric.IMPRESSIONS] = impressionsInsights;
        }

        if (metrics.includes(MalouMetric.ENGAGEMENTS)) {
            const engagementsInsights: DailyValue[] = impressionsAndEngagementsByDays
                .flatMap((impressionsAndEngagementsByDay: ImpressionsAndEngagementsByDay) => impressionsAndEngagementsByDay.engagements)
                .map((impressionsValue: ValueWithEndTime) => ({
                    value: impressionsValue.value,
                    date: new Date(impressionsValue.end_time),
                }));
            insightsByDay[MalouMetric.ENGAGEMENTS] = engagementsInsights;
        }

        results.forEach((result) => {
            insightsByDay[result.metric] = result.data;
        });

        if (!Object.keys(insightsByDay).length) {
            return { error: true, message: MalouErrorCode.INSIGHTS_NOT_FOUND };
        }

        return insightsByDay;
    };

    private _getPlatformAccess = async (restaurantId: ID) => {
        try {
            const platform = await this._platformsRepository.findOne({
                filter: { restaurantId, key: PlatformKey.FACEBOOK },
                options: { lean: true },
            });
            if (!platform) {
                return { error: true, message: MalouErrorCode.PLATFORM_NOT_FOUND };
            }
            let credentialId: DbId | null = null;
            let pageId: string | null = null;
            if (platform && platform?.credentials?.length) {
                credentialId = platform.credentials[0] ?? null;
            }
            if (platform && platform?.socialId) {
                pageId = platform.socialId;
            }
            if (!credentialId) {
                return { error: true, message: MalouErrorCode.PLATFORM_MISSING_PERMISSIONS };
            }
            return { credentialId, pageId };
        } catch (err) {
            logger.warn('[ERROR_FB_CREDENTIALS]', err);
            return { error: true, message: MalouErrorCode.PLATFORM_MISSING_PERMISSIONS };
        }
    };

    private _getImpressionsAndEngagementsByDay = async (
        credentialId: string,
        pageId: string,
        intervals
    ): Promise<ImpressionsAndEngagementsByDay[]> => {
        const impressionsEngagementsPromises: Promise<ImpressionsAndEngagementsByDay>[] = [];
        // we need to loop because insight facebook api max range is 93 days
        for (const element of intervals) {
            const impressionsEngagementsPromise: Promise<ImpressionsAndEngagementsByDay> = facebookCredentialsUseCases
                .getInsights(credentialId, pageId, element.since, element.until)
                .then((insight: IFacebookDataWithPaging<IFacebookInsights>) => {
                    const insightMapped: ImpressionsAndEngagementsByDay = {
                        impressions: [],
                        engagements: [],
                    };
                    insight.data?.forEach((metric: IFacebookInsights) => {
                        if (metric.name === 'page_impressions') {
                            insightMapped.impressions.push(...metric.values);
                        }
                        if (metric.name === 'page_post_engagements') {
                            insightMapped.engagements.push(...metric.values);
                        }
                    });
                    return insightMapped;
                });
            impressionsEngagementsPromises.push(impressionsEngagementsPromise);
        }
        return Promise.all(impressionsEngagementsPromises);
    };

    private _getFollowersByDay = async (pageId: string, startDate: Date, endDate: Date): Promise<DailyValue[]> => {
        const followersInsights = await this._platformInsightsRepository.find({
            filter: {
                metric: StoredInDBInsightsMetric.FOLLOWERS,
                socialId: pageId,
                platformKey: PlatformKey.FACEBOOK,
                date: { $gte: startDate, $lte: endDate },
            },
            options: { sort: { createdAt: -1 }, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
        return followersInsights
            .map((f) => (isNotNil(f.value) ? { date: new Date(f.year, f.month, f.day), value: f.value } : null))
            .filter(isNotNil);
    };

    private _getPosts = async (credentialId: string, pageId: string, intervals): Promise<DailyValue[]> => {
        const postsInsights = await facebookCredentialsUseCases.getPagePublishedPosts(
            credentialId,
            pageId,
            new Date(intervals[0].since),
            new Date(intervals[intervals.length - 1].until)
        );
        return postsInsights
            .filter((p) => !!p.attachments?.data?.[0]?.media?.image?.src && p.attachments?.data[0]?.media_type !== 'event')
            .map((p) => (p.created_time ? { value: 1, date: new Date(p.created_time) } : null))
            .filter(isNotNil);
    };

    insertTodayFollowers = async (restaurantId: string) => {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.FACEBOOK);
        if (!platform) {
            return { error: true, message: MalouErrorCode.PLATFORM_NOT_FOUND };
        }
        const { key, socialId: pageId, credentials } = platform;

        if (!credentials?.length || !pageId) {
            return { error: true, message: MalouErrorCode.PLATFORM_MISSING_PERMISSIONS };
        }

        const { followers_count: followersCount } = await tryForCredentials(credentials, (credentialId) =>
            facebookCredentialsUseCases.getFollowers(credentialId, pageId)
        );
        const today = DateTime.local();
        const { year, month, day } = today;
        const filter = {
            platformKey: key,
            socialId: pageId,
            metric: StoredInDBInsightsMetric.FOLLOWERS,
            year,
            month: month - 1,
            day,
        };
        const insight = {
            ...filter,
            value: followersCount,
            date: today.toJSDate(),
        };
        return this._platformInsightsRepository.upsert({ filter, update: insight });
    };

    async fetchTodayRating(): Promise<undefined> {
        return;
    }
}
