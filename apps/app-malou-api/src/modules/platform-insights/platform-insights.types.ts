import { AggregationTimeScale, InsightsTab, MalouMetric } from '@malou-io/package-utils';

export type InsightError = {
    error?: boolean;
    message?: string;
};
export type Value = {
    value?: number;
};

export type DailyValue = {
    date: Date;
} & Value;

export type WeeklyValue = {
    weekStart: Date;
} & Value;

export type MonthlyValue = {
    monthStart: Date;
} & Value;

export type MetricToDataValue<DateValue extends Value | DailyValue | WeeklyValue | MonthlyValue = DailyValue> = {
    [K in MalouMetric]?: DateValue;
} & InsightError;

export type MetricToDataValues<DateValue extends Value | DailyValue | WeeklyValue | MonthlyValue = DailyValue> = {
    [K in MalouMetric]?: DateValue[];
} & InsightError;

export type TimeScaleToMetricToDataValues = {
    [AggregationTimeScale.TOTAL]?: MetricToDataValue<Value>;
    [AggregationTimeScale.BY_DAY]?: MetricToDataValues<DailyValue>;
    [AggregationTimeScale.BY_WEEK]?: MetricToDataValues<WeeklyValue>;
    [AggregationTimeScale.BY_MONTH]?: MetricToDataValues<MonthlyValue>;
} & InsightError;

export enum TotalComputationRule {
    SUM,
    TAKE_LAST_ITEM,
}

export interface DownloadInsightsAsPdfBody {
    callbackUrl: string;
    params: string;
    jwtToken: string;
    restaurantId?: string;
    insightTab: InsightsTab;
    userId?: string;
}

export type RatingValue = number | null;

export interface PlatformInsightUseCase {
    fetchTodayRating: (restaurantId: string) => Promise<RatingValue | undefined>;
    getInsightsAggregated: (
        restaurantId: string,
        metrics: MalouMetric[],
        aggregators: AggregationTimeScale[],
        filters: any
    ) => Promise<TimeScaleToMetricToDataValues>;
    insertTodayFollowers;
}
