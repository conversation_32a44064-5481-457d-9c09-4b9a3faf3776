import { StoryToDuplicateDto } from '@malou-io/package-dto';
import { EntityConstructor, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { SocialPostMedia } from ':modules/posts/v2/entities/social-post-media.entity';

type StoryToDuplicateProps = EntityConstructor<StoryToDuplicate> & { id: string };

export class StoryToDuplicate {
    id: string;
    bindingId: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    plannedPublicationDate: Date | null;
    medias: SocialPostMedia[];
    socialCreatedAt: Date | null;

    constructor(data: StoryToDuplicateProps) {
        this.id = data.id;
        this.bindingId = data.bindingId;
        this.platformKeys = data.platformKeys;
        this.published = data.published;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.medias = data.medias;
        this.socialCreatedAt = data.socialCreatedAt;
    }

    toDto(): StoryToDuplicateDto {
        return {
            id: this.id,
            bindingId: this.bindingId,
            platformKeys: this.platformKeys,
            published: this.published,
            plannedPublicationDate: this.plannedPublicationDate?.toISOString() ?? null,
            medias: this.medias.map((media) => media.toDto()),
            socialCreatedAt: this.socialCreatedAt?.toISOString() ?? null,
        };
    }
}
