import { singleton } from 'tsyringe';

import { IPost, toDbId } from '@malou-io/package-models';
import { PostPublicationStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { UpdateMediaPostIdsService } from ':modules/media/services/update-media-post-ids.service';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class StoriesService {
    constructor(
        private readonly _storiesRepository: StoriesRepository,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _updateMediaPostIdsService: UpdateMediaPostIdsService
    ) {}

    async deleteById(postId: string): Promise<{ success: boolean }> {
        let post: IPost | null;
        try {
            post = await this._storiesRepository.findById(postId);
            if (!post) {
                return { success: true };
            }
        } catch (error) {
            if (error instanceof MalouError) {
                return { success: false };
            }
            throw error;
        }

        if (post.published === PostPublicationStatus.PUBLISHED) {
            return { success: false };
        }

        await this._updateMediaPostIdsService.updateMediaPostIds(postId, { $pull: { postIds: postId } });
        await this._agendaSingleton.deleteJobs({
            'data.postId': { $in: [postId, toDbId(postId)] },
            name: {
                $in: [
                    AgendaJobName.PREPARE_POST,
                    AgendaJobName.PUBLISH_POST_ON_PLATFORM,
                    AgendaJobName.PUBLISH_POST,
                    AgendaJobName.FETCH_AND_MATCH_FB_POST_TIMED_OUT,
                    AgendaJobName.FETCH_POST_AND_CHECK_ERRORS,
                ],
            },
        });

        const result = await this._storiesRepository.deleteStoryById(postId);
        return { success: result.acknowledged && result.deletedCount === 1 };
    }
}
