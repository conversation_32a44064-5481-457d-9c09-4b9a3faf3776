import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';

@singleton()
export class ScheduleStoryPublicationService {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    async scheduleStoryPublication(userId: string, storyId: string, date: Date): Promise<void> {
        await this.cancelPostPublication(storyId);
        await this._agendaSingleton.schedule(date, AgendaJobName.PUBLISH_STORIES_ON_PLATFORM, {
            userId: toDbId(userId),
            storyId: toDbId(storyId),
        });

        logger.info('[STORY PUBLICATION] Rescheduled story publication', { storyId, date });
    }

    async cancelPostPublication(storyId: string): Promise<void> {
        const deletedJobsCount = await this._agendaSingleton.deleteJobs({
            name: AgendaJobName.PUBLISH_STORIES_ON_PLATFORM,
            'data.storyId': { $in: [storyId, toDbId(storyId)] },
        });

        logger.info('[STORY PUBLICATION] Cancelled story publication', { storyId, deletedJobsCount });
    }
}
