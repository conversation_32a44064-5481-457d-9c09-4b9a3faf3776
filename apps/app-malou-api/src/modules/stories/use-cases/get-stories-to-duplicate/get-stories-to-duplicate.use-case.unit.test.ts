import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { StoryToDuplicateDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetStoriesToDuplicateUseCase } from ':modules/stories/use-cases/get-stories-to-duplicate/get-stories-to-duplicate.use-case';

describe('GetStoriesToDuplicateUseCase', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository', 'RestaurantsRepository', 'MediasRepository']);
    });

    describe('Empty result cases', () => {
        it('should return an empty array if no story IDs are provided', async () => {
            const getStoriesToDuplicateUseCase = container.resolve(GetStoriesToDuplicateUseCase);
            const result = await getStoriesToDuplicateUseCase.execute([], []);

            expect(result).toEqual([]);
        });

        it('should return an empty array if stories do not exist', async () => {
            const getStoriesToDuplicateUseCase = container.resolve(GetStoriesToDuplicateUseCase);
            const nonExistentStoryIds = [newDbId().toString(), newDbId().toString()];
            const nonExistentBindingIds = [newDbId().toString(), newDbId().toString()];

            const result = await getStoriesToDuplicateUseCase.execute(nonExistentStoryIds, nonExistentBindingIds);

            expect(result).toEqual([]);
        });

        it('should return an empty array if posts exist but are not stories', async () => {
            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(false) // Not a story
                                    .source(PostSource.SOCIAL)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const postId = seededObjects.posts[0]._id.toString();

            const getStoriesToDuplicateUseCase = container.resolve(GetStoriesToDuplicateUseCase);
            const result = await getStoriesToDuplicateUseCase.execute([postId], []);

            expect(result).toEqual([]);
        });
    });

    describe('Success cases', () => {
        it('should return stories to duplicate by story IDs', async () => {
            const plannedDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const socialCreatedAt = DateTime.now().minus({ hours: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants' | 'medias'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    medias: {
                        data() {
                            return [getDefaultMedia().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .keys([])
                                    .published(PostPublicationStatus.DRAFT)
                                    .plannedPublicationDate(plannedDate)
                                    .socialCreatedAt(socialCreatedAt)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .attachments([dependencies.medias()[0]._id])
                                    .bindingId('binding-123')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoryToDuplicateDto[] {
                    const story = dependencies.posts[0];
                    return [
                        {
                            id: story._id.toString(),
                            bindingId: story.bindingId!,
                            platformKeys: [story.key!],
                            published: story.published,
                            plannedPublicationDate: story.plannedPublicationDate!.toISOString(),
                            medias: expect.any(Array),
                            socialCreatedAt: story.socialCreatedAt!.toISOString(),
                        },
                    ];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const storyId = seededObjects.posts[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesToDuplicateUseCase = container.resolve(GetStoriesToDuplicateUseCase);
            const result = await getStoriesToDuplicateUseCase.execute([storyId], []);

            expect(result).toEqual(expectedResult);
        });

        it('should return stories to duplicate by binding IDs', async () => {
            const plannedDate = DateTime.now().plus({ days: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .key(undefined)
                                    .keys([PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK])
                                    .published(PostPublicationStatus.PENDING)
                                    .plannedPublicationDate(plannedDate)
                                    .socialCreatedAt(undefined)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .attachments([])
                                    .bindingId('binding-456')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoryToDuplicateDto[] {
                    const story = dependencies.posts[0];
                    return [
                        {
                            id: story._id.toString(),
                            bindingId: story.bindingId!,
                            platformKeys: story.keys!,
                            published: story.published,
                            plannedPublicationDate: story.plannedPublicationDate!.toISOString(),
                            medias: [],
                            socialCreatedAt: null,
                        },
                    ];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const bindingId = seededObjects.posts[0].bindingId!;
            const expectedResult = testCase.getExpectedResult();

            const getStoriesToDuplicateUseCase = container.resolve(GetStoriesToDuplicateUseCase);
            const result = await getStoriesToDuplicateUseCase.execute([], [bindingId]);

            expect(result).toEqual(expectedResult);
        });

        it('should return multiple stories to duplicate by mixed IDs and binding IDs', async () => {
            const plannedDate1 = DateTime.now().plus({ days: 1 }).toJSDate();
            const plannedDate2 = DateTime.now().plus({ days: 2 }).toJSDate();
            const socialCreatedAt = DateTime.now().minus({ hours: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .keys([])
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .plannedPublicationDate(plannedDate1)
                                    .socialCreatedAt(socialCreatedAt)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .attachments([])
                                    .bindingId('binding-story-1')
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .key(undefined)
                                    .keys([PlatformKey.FACEBOOK])
                                    .published(PostPublicationStatus.DRAFT)
                                    .plannedPublicationDate(plannedDate2)
                                    .socialCreatedAt(undefined)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .attachments([])
                                    .bindingId('binding-story-2')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoryToDuplicateDto[] {
                    const story1 = dependencies.posts[0];
                    const story2 = dependencies.posts[1];
                    return [
                        {
                            id: story1._id.toString(),
                            bindingId: story1.bindingId!,
                            platformKeys: [story1.key!],
                            published: story1.published,
                            plannedPublicationDate: story1.plannedPublicationDate!.toISOString(),
                            medias: [],
                            socialCreatedAt: story1.socialCreatedAt!.toISOString(),
                        },
                        {
                            id: story2._id.toString(),
                            bindingId: story2.bindingId!,
                            platformKeys: story2.keys!,
                            published: story2.published,
                            plannedPublicationDate: story2.plannedPublicationDate!.toISOString(),
                            medias: [],
                            socialCreatedAt: null,
                        },
                    ];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const story1Id = seededObjects.posts[0]._id.toString();
            const story2BindingId = seededObjects.posts[1].bindingId!;
            const expectedResult = testCase.getExpectedResult();

            const getStoriesToDuplicateUseCase = container.resolve(GetStoriesToDuplicateUseCase);
            const result = await getStoriesToDuplicateUseCase.execute([story1Id], [story2BindingId]);

            expect(result).toEqual(expectedResult);
        });

        it('should handle stories with null planned publication date', async () => {
            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .key(PlatformKey.INSTAGRAM)
                                    .keys([])
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .plannedPublicationDate(null)
                                    .socialCreatedAt(undefined)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .attachments([])
                                    .bindingId('binding-no-date')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoryToDuplicateDto[] {
                    const story = dependencies.posts[0];
                    return [
                        {
                            id: story._id.toString(),
                            bindingId: story.bindingId!,
                            platformKeys: [story.key!],
                            published: story.published,
                            plannedPublicationDate: null,
                            medias: [],
                            socialCreatedAt: null,
                        },
                    ];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const storyId = seededObjects.posts[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const getStoriesToDuplicateUseCase = container.resolve(GetStoriesToDuplicateUseCase);
            const result = await getStoriesToDuplicateUseCase.execute([storyId], []);

            expect(result).toEqual(expectedResult);
        });

        it('should handle stories with different publication statuses', async () => {
            const plannedDate = DateTime.now().plus({ days: 3 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .key(undefined)
                                    .keys([PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK])
                                    .published(PostPublicationStatus.ERROR)
                                    .plannedPublicationDate(plannedDate)
                                    .socialCreatedAt(undefined)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .attachments([])
                                    .bindingId('binding-error')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): StoryToDuplicateDto[] {
                    const story = dependencies.posts[0];
                    return [
                        {
                            id: story._id.toString(),
                            bindingId: story.bindingId!,
                            platformKeys: story.keys!,
                            published: story.published,
                            plannedPublicationDate: story.plannedPublicationDate!.toISOString(),
                            medias: [],
                            socialCreatedAt: null,
                        },
                    ];
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const bindingId = seededObjects.posts[0].bindingId!;
            const expectedResult = testCase.getExpectedResult();

            const getStoriesToDuplicateUseCase = container.resolve(GetStoriesToDuplicateUseCase);
            const result = await getStoriesToDuplicateUseCase.execute([], [bindingId]);

            expect(result).toEqual(expectedResult);
        });
    });
});
