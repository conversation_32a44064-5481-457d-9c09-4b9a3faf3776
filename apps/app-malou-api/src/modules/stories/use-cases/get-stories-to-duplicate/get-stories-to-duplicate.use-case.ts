import { singleton } from 'tsyringe';

import { StoryToDuplicateDto } from '@malou-io/package-dto';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class GetStoriesToDuplicateUseCase {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute(storyIds: string[], storyBindingIds: string[]): Promise<StoryToDuplicateDto[]> {
        const storiesToDuplicate = await this._storiesRepository.getStoriesToDuplicate(storyIds, storyBindingIds);
        return storiesToDuplicate.map((story) => story.toDto());
    }
}
