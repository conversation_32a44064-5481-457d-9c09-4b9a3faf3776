import { singleton } from 'tsyringe';

import { GetPublishedStoriesCountDto } from '@malou-io/package-dto';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class GetPublishedStoriesCountUseCase {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute(restaurantId: string, startDate: Date, endDate: Date): Promise<GetPublishedStoriesCountDto> {
        const count = await this._storiesRepository.getPublishedStoriesCount(restaurantId, startDate, endDate);
        return {
            count,
        };
    }
}
