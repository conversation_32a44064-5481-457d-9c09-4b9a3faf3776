import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { GetPublishedStoriesCountDto } from '@malou-io/package-dto';
import { PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetPublishedStoriesCountUseCase } from ':modules/stories/use-cases/get-published-stories-count/get-published-stories-count.use-case';

describe('GetPublishedStoriesCountUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'PostsRepository']);
    });

    const startDate = DateTime.fromISO('2024-01-01T00:00:00.000Z').toJSDate();
    const endDate = DateTime.fromISO('2024-01-31T23:59:59.999Z').toJSDate();

    describe('0 result cases', () => {
        it('should return 0 if there are no posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });

        it('should return 0 if restaurant has no posts', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('123').build(), getDefaultRestaurant().uniqueKey('456').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-15T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });

        it('should return 0 if restaurant has no stories (only regular posts)', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(false) // Not a story
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-15T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });

        it('should return 0 if restaurant has stories but not published', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.DRAFT) // Not published
                                    .socialCreatedAt(DateTime.fromISO('2024-01-15T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.ERROR) // Not published
                                    .socialCreatedAt(DateTime.fromISO('2024-01-20T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });

        it('should return 0 if restaurant has published stories but outside date range', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2023-12-31T23:59:59.000Z').toJSDate()) // Before range
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-02-01T00:00:01.000Z').toJSDate()) // After range
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });

        it('should return 0 if restaurant has stories but not from social source', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SEO) // Not social source
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-15T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 0 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });
    });

    describe('Positive result cases', () => {
        it('should return 1 if restaurant has one published story in date range', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-15T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 1 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });

        it('should return 3 if restaurant has multiple published stories in date range', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-05T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-15T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-25T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                // This one should not be counted (draft)
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.DRAFT)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-10T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                // This one should not be counted (outside date range)
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-02-01T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 3 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });

        it('should return correct count at date range boundaries', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                // Exactly at start boundary - should be included
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(startDate)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                // Exactly at end boundary - should be included
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(endDate)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 2 };
                },
            });

            await testCase.build();

            const restaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(restaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });

        it('should only count stories for the specified restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('target').build(), getDefaultRestaurant().uniqueKey('other').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [
                                // Stories for target restaurant
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-15T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-20T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                // Stories for other restaurant - should not be counted
                                getDefaultPost()
                                    .isStory(true)
                                    .source(PostSource.SOCIAL)
                                    .published(PostPublicationStatus.PUBLISHED)
                                    .socialCreatedAt(DateTime.fromISO('2024-01-10T12:00:00.000Z').toJSDate())
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (): GetPublishedStoriesCountDto => {
                    return { count: 2 };
                },
            });

            await testCase.build();

            const targetRestaurantId = testCase.getSeededObjects().restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const useCase = container.resolve(GetPublishedStoriesCountUseCase);
            const result = await useCase.execute(targetRestaurantId, startDate, endDate);

            expect(result).toEqual(expectedResult);
        });
    });
});
