import { singleton } from 'tsyringe';

import { StoryDto } from '@malou-io/package-dto';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@singleton()
export class GetStoryByIdUseCase {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute(storyId: string): Promise<StoryDto> {
        const story = await this._storiesRepository.getStoryById(storyId);

        if (!story) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { message: 'Story not found', metadata: { storyId } });
        }

        return story.toDto();
    }
}
