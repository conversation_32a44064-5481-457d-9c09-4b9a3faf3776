import { singleton } from 'tsyringe';

import { StoriesService } from ':modules/stories/services/stories.service';

@singleton()
export class DeleteStoriesUseCase {
    constructor(private readonly _storiesService: StoriesService) {}

    async execute(storyIds: string[]): Promise<{ storyId: string; success: boolean }[]> {
        const results: { storyId: string; success: boolean }[] = [];
        for (const storyId of storyIds) {
            const result = await this._storiesService.deleteById(storyId);
            results.push({ storyId, success: result.success });
        }
        return results;
    }
}
