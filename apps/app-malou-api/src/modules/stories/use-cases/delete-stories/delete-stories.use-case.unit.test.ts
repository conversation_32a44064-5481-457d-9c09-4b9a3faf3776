import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PlatformKey, PostPublicationStatus, PostSource } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { DeleteStoriesUseCase } from ':modules/stories/use-cases/delete-stories/delete-stories.use-case';

describe('DeleteStoriesUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['PostsRepository', 'MediasRepository']);
    });

    it('should delete all stories', async () => {
        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.DRAFT).build(),
                            getDefaultPost()
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .published(PostPublicationStatus.PENDING)
                                .key(PlatformKey.FACEBOOK)
                                .build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.ERROR).build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): { storyId: string; success: boolean }[] {
                return [
                    { storyId: dependencies.posts[0]._id.toString(), success: true },
                    { storyId: dependencies.posts[1]._id.toString(), success: true },
                    { storyId: dependencies.posts[2]._id.toString(), success: true },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId1 = seededObjects.posts[0]._id.toString();
        const storyId2 = seededObjects.posts[1]._id.toString();
        const storyId3 = seededObjects.posts[2]._id.toString();

        const deleteStoriesUseCase = container.resolve(DeleteStoriesUseCase);
        const result = await deleteStoriesUseCase.execute([storyId1, storyId2, storyId3]);

        const expectedResult = testCase.getExpectedResult();

        expect(result).toIncludeSameMembers(expectedResult);
    });

    it('should not delete a published story but delete the other stories', async () => {
        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.DRAFT).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.PUBLISHED).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.PENDING).build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): { storyId: string; success: boolean }[] {
                return [
                    { storyId: dependencies.posts[0]._id.toString(), success: true },
                    { storyId: dependencies.posts[1]._id.toString(), success: false },
                    { storyId: dependencies.posts[2]._id.toString(), success: true },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId1 = seededObjects.posts[0]._id.toString();
        const storyId2 = seededObjects.posts[1]._id.toString();
        const storyId3 = seededObjects.posts[2]._id.toString();

        const deleteStoriesUseCase = container.resolve(DeleteStoriesUseCase);
        const result = await deleteStoriesUseCase.execute([storyId1, storyId2, storyId3]);

        const expectedResult = testCase.getExpectedResult();

        expect(result).toIncludeSameMembers(expectedResult);
    });

    it('should not delete a non-story post but delete the stories', async () => {
        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.DRAFT).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(false).published(PostPublicationStatus.DRAFT).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.PENDING).build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): { storyId: string; success: boolean }[] {
                return [
                    { storyId: dependencies.posts[0]._id.toString(), success: true },
                    { storyId: dependencies.posts[1]._id.toString(), success: true }, // Not a story, return success
                    { storyId: dependencies.posts[2]._id.toString(), success: true },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId1 = seededObjects.posts[0]._id.toString();
        const postId2 = seededObjects.posts[1]._id.toString();
        const storyId3 = seededObjects.posts[2]._id.toString();

        const deleteStoriesUseCase = container.resolve(DeleteStoriesUseCase);
        const result = await deleteStoriesUseCase.execute([storyId1, postId2, storyId3]);

        const expectedResult = testCase.getExpectedResult();

        expect(result).toIncludeSameMembers(expectedResult);
    });

    it('should not delete a SEO post but delete the stories', async () => {
        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.DRAFT).build(),
                            getDefaultPost().source(PostSource.SEO).isStory(false).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.PENDING).build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): { storyId: string; success: boolean }[] {
                return [
                    { storyId: dependencies.posts[0]._id.toString(), success: true },
                    { storyId: dependencies.posts[1]._id.toString(), success: true },
                    { storyId: dependencies.posts[2]._id.toString(), success: true },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId1 = seededObjects.posts[0]._id.toString();
        const seoPostId = seededObjects.posts[1]._id.toString();
        const storyId3 = seededObjects.posts[2]._id.toString();

        const deleteStoriesUseCase = container.resolve(DeleteStoriesUseCase);
        const result = await deleteStoriesUseCase.execute([storyId1, seoPostId, storyId3]);

        const expectedResult = testCase.getExpectedResult();

        expect(result).toIncludeSameMembers(expectedResult);
    });

    it('should delete the stories and consider successful the deletion of story that does not exist', async () => {
        const fakeStoryId = newDbId().toString();

        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.DRAFT).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.PENDING).build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): { storyId: string; success: boolean }[] {
                return [
                    { storyId: dependencies.posts[0]._id.toString(), success: true },
                    { storyId: dependencies.posts[1]._id.toString(), success: true },
                    { storyId: fakeStoryId, success: true },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyId1 = seededObjects.posts[0]._id.toString();
        const storyId2 = seededObjects.posts[1]._id.toString();

        const deleteStoriesUseCase = container.resolve(DeleteStoriesUseCase);
        const result = await deleteStoriesUseCase.execute([storyId1, storyId2, fakeStoryId]);

        const expectedResult = testCase.getExpectedResult();

        expect(result).toIncludeSameMembers(expectedResult);
    });

    it('should handle empty array of story IDs', async () => {
        const deleteStoriesUseCase = container.resolve(DeleteStoriesUseCase);
        const result = await deleteStoriesUseCase.execute([]);

        expect(result).toEqual([]);
    });

    it('should handle mixed scenarios with published, non-story, and non-existent stories', async () => {
        const fakeStoryId = newDbId().toString();

        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.DRAFT).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.PUBLISHED).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(false).published(PostPublicationStatus.DRAFT).build(),
                            getDefaultPost().source(PostSource.SEO).isStory(false).build(),
                            getDefaultPost().source(PostSource.SOCIAL).isStory(true).published(PostPublicationStatus.ERROR).build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies): { storyId: string; success: boolean }[] {
                return [
                    { storyId: dependencies.posts[0]._id.toString(), success: true }, // Draft story - should delete
                    { storyId: dependencies.posts[1]._id.toString(), success: false }, // Published story - should not delete
                    { storyId: dependencies.posts[2]._id.toString(), success: true }, // Non-story social post - should not delete but return success because it's not a story
                    { storyId: dependencies.posts[3]._id.toString(), success: true }, // SEO post - should not delete but return success because it's not a story
                    { storyId: dependencies.posts[4]._id.toString(), success: true }, // Error story - should delete
                    { storyId: fakeStoryId, success: true }, // Non-existent - should return success
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const storyIds = seededObjects.posts.map((post) => post._id.toString()).concat([fakeStoryId]);

        const deleteStoriesUseCase = container.resolve(DeleteStoriesUseCase);
        const result = await deleteStoriesUseCase.execute(storyIds);

        const expectedResult = testCase.getExpectedResult();

        expect(result).toIncludeSameMembers(expectedResult);
    });
});
