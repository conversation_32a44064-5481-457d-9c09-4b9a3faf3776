import { compact } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { StoryDto, UpdateStoryDto } from '@malou-io/package-dto';
import { MalouErrorCode, MediaType, PostPublicationStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import { UpdateMediaPostIdsService } from ':modules/media/services/update-media-post-ids.service';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { SchedulePostPublicationService } from ':modules/posts/v2/services/schedule-post-publication/schedule-post-publication.service';
import { Story } from ':modules/stories/entities/story.entity';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class UpdateStoryUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _storiesRepository: StoriesRepository,
        private readonly _schedulePostPublicationService: SchedulePostPublicationService,
        private readonly _slackService: SlackService,
        private readonly _updateMediaPostIdsService: UpdateMediaPostIdsService
    ) {}

    async execute({ story, author }: { story: UpdateStoryDto; author: PostAuthorProps }): Promise<StoryDto> {
        try {
            logger.info('[STORY PUBLICATION] [UPDATE] Started', { story, author });

            const storyBeforeUpdate = await this._storiesRepository.findOne({ filter: { _id: story.id }, options: { lean: true } });

            if (!storyBeforeUpdate) {
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { metadata: { storyId: story.id } });
            }

            if (storyBeforeUpdate.published === PostPublicationStatus.PENDING && storyBeforeUpdate.isPublishing) {
                throw new MalouError(MalouErrorCode.POST_IS_PUBLISHING, { metadata: { storyId: story.id } });
            }

            const plannedPublicationDate = story.plannedPublicationDate ? new Date(story.plannedPublicationDate) : new Date();

            for (const media of story.medias) {
                await this._mediasRepository.updateTransformData(media.id, media.transformData);
            }

            const storyAuthor = new PostAuthor({ id: author.id, name: author.name, lastname: author.lastname });
            const newPost = await this._storiesRepository.updateStory(story.id, story, storyAuthor, plannedPublicationDate);
            assert(newPost);
            const dto = newPost.toDto();

            if (dto.published === PostPublicationStatus.DRAFT) {
                // TODO posts-v2 This code needs to be removed/refactored when post v1 is removed
                await this._updateMediaPostIdsService.updateMediaPostIds(story.id, { $pull: { postIds: story.id } });
            } else {
                // TODO posts-v2 This code needs to be removed/refactored when post v1 is removed
                await this._updateMediaPostIdsService.updateMediaPostIds(story.id, { $addToSet: { postIds: story.id } });
            }

            if (dto.published === PostPublicationStatus.PENDING) {
                await this._schedulePostPublicationService.schedulePostPublication(author.id, dto.id, plannedPublicationDate);
            } else {
                await this._schedulePostPublicationService.cancelPostPublication(dto.id);
            }

            logger.info('[STORY PUBLICATION] [UPDATE] Finished', story);
            return dto;
        } catch (err: unknown) {
            logger.error('[STORY PUBLICATION] [UPDATE] Error', { story, author });
            if (!(err instanceof MalouError)) {
                this._slackService.sendAlert({ data: { err }, channel: SlackChannel.POSTS_V2_ALERTS });
            }
            throw err;
        }
    }

    private _getMediasFromPostMediasDto(medias: StoryDto['medias']): Story['medias'] {
        return compact(
            medias.map((attachment) => {
                const base = {
                    id: attachment.id,
                    type: attachment.type,
                    aiDescription: attachment.aiDescription,
                    thumbnail1024OutsideUrl: attachment.thumbnail1024OutsideUrl,
                    thumbnail256OutsideUrl: attachment.thumbnail256OutsideUrl,
                    thumbnail256OutsideDimensions: attachment.thumbnail256OutsideDimensions,
                    thumbnail1024OutsideDimensions: attachment.thumbnail1024OutsideDimensions,
                    transformData: attachment.transformData,
                    aspectRatio: attachment.aspectRatio,
                };
                if (attachment.type === MediaType.PHOTO) {
                    return {
                        ...base,
                        type: MediaType.PHOTO,
                    };
                }
                if (attachment.type === MediaType.VIDEO) {
                    return {
                        ...base,
                        type: MediaType.VIDEO,
                        videoUrl: attachment.videoUrl,
                        videoDimensions: {
                            width: attachment.videoDimensions.width,
                            height: attachment.videoDimensions.height,
                        },
                    };
                }
            })
        );
    }

    private _getStoryFeedbacksFromStoryFeedbacksDto(storyFeedbacks: StoryDto['feedbacks']): Story['feedbacks'] {
        return storyFeedbacks
            ? {
                  id: storyFeedbacks.id,
                  isOpen: storyFeedbacks.isOpen,
                  participants: storyFeedbacks.participants.map((participant) => ({
                      participant: {
                          id: participant.participant.id,
                          name: participant.participant.name,
                          lastname: participant.participant.lastname ?? undefined,
                          email: participant.participant.email,
                          role: participant.participant.role ?? undefined,
                      },
                      types: participant.types,
                  })),
                  feedbackMessages: storyFeedbacks.feedbackMessages.map((message) => ({
                      id: message.id,
                      author: {
                          id: message.author.id,
                          name: message.author.name,
                          lastname: message.author.lastname ?? undefined,
                          profilePictureUrl: message.author.profilePictureUrl ?? undefined,
                          email: message.author.email,
                          role: message.author.role ?? undefined,
                      },
                      createdAt: new Date(message.createdAt),
                      updatedAt: new Date(message.updatedAt),
                      text: message.text,
                      type: message.type,
                      visibility: message.visibility,
                  })),
                  updatedAt: new Date(storyFeedbacks.updatedAt),
                  createdAt: new Date(storyFeedbacks.createdAt),
              }
            : undefined;
    }
}
