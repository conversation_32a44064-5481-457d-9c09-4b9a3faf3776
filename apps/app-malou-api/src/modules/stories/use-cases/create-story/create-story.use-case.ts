import { singleton } from 'tsyringe';

import { StoryDto } from '@malou-io/package-dto';
import { DeviceType, getFeatureFlaggedPlatforms, getPlatformKeysWithStories, PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { Story } from ':modules/stories/entities/story.entity';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { isFeatureAvailableForUser } from ':services/experimentations-service/experimentation.service';

@singleton()
export class CreateStoryUseCase {
    constructor(
        private readonly _storiesRepository: StoriesRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async execute({
        restaurantId,
        author,
        date,
        createdFromDeviceType,
    }: {
        restaurantId: string;
        author: PostAuthorProps;
        date?: Date;
        createdFromDeviceType: DeviceType;
    }): Promise<StoryDto> {
        logger.info('[CREATE STORY] started', { restaurantId, author });

        const platforms = await this._platformsRepository.getPlatformsByRestaurantId(restaurantId);
        const keys = await this._getStoriesPlatformKeys(platforms, author);

        const postAuthor = new PostAuthor(author);

        const createStory: Story = Story.createEmpty({
            platformKeys: keys,
            author: postAuthor,
            date,
            createdFromDeviceType,
        });

        const createdStory = await this._storiesRepository.createStory(createStory, restaurantId);
        const dto = createdStory.toDto();

        logger.info('[CREATE STORY] finished', dto);
        return dto;
    }

    private async _getStoriesPlatformKeys(platforms: Platform[], user: PostAuthorProps): Promise<PlatformKey[]> {
        const basePlatformKeys = getPlatformKeysWithStories();
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();

        const featureFlaggedPlatformsEnabledForUser = await Promise.all(
            featureFlaggedPlatforms.map(async (ffPlatform) => {
                const enabled = ffPlatform.featureFlagKey
                    ? await isFeatureAvailableForUser({ userId: user.id, featureName: ffPlatform.featureFlagKey })
                    : true;
                return { key: ffPlatform.key, enabled };
            })
        );

        const platformKeysEnabled = basePlatformKeys.filter((key) => {
            const featureFlaggedPlatform = featureFlaggedPlatforms.find((ffPlatform) => ffPlatform.key === key);
            return (
                !featureFlaggedPlatform ||
                featureFlaggedPlatformsEnabledForUser.some((platform) => platform.key === featureFlaggedPlatform.key)
            );
        });

        const connectedSocialPlatforms = platforms.filter((platform) => platformKeysEnabled.includes(platform.key));

        return connectedSocialPlatforms.map((platform) => platform.key);
    }
}
