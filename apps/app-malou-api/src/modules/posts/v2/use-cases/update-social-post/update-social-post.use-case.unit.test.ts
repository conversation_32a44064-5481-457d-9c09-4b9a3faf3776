import { omit } from 'lodash';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { SocialPostDto } from '@malou-io/package-dto';
import { IPost, newDbId, toDbId } from '@malou-io/package-models';
import {
    AspectRatio,
    CaslRole,
    DeviceType,
    HashtagType,
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
    TiktokPrivacyStatus,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { getDefaultFeedback } from ':modules/posts/v2/tests/feedbacks.builder';
import { UpdateSocialPostUseCase } from ':modules/posts/v2/use-cases/update-social-post/update-social-post.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';
import { SlackService } from ':services/slack.service';

describe('UpdateSocialPostUseCase', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'UsersRepository',
            'UserRestaurantsRepository',
            'PostsRepository',
            'MediasRepository',
            'FeedbacksRepository',
        ]);

        const slackServiceMock = {
            sendMessage: jest.fn(),
            createContextForSlack: jest.fn(),
            sendAlert: jest.fn(),
        } as unknown as SlackService;
        container.register(SlackService, { useValue: slackServiceMock });
    });

    it('should update a post and return it', async () => {
        const updateSocialPostUseCase = container.resolve(UpdateSocialPostUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'medias' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).source(PostSource.SOCIAL).build()];
                    },
                },
                medias: {
                    data(dependencies) {
                        return [
                            getDefaultMedia()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .transformData({
                                    aspectRatio: AspectRatio.ORIGINAL,
                                    width: 200,
                                    height: 200,
                                    rotationInDegrees: 0,
                                    left: 0,
                                    top: 0,
                                })
                                .aspectRatio(1)
                                .storedObjects({
                                    thumbnail1024Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail1024Outside',
                                        provider: 'S3',
                                    },
                                    thumbnail256Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail256Outside',
                                        provider: 'S3',
                                    },
                                    original: { key: 'key', publicUrl: 'http://www.url.com/original', provider: 'S3' },
                                    normalized: { key: 'key', publicUrl: 'http://www.url.com/normalized', provider: 'S3' },
                                })
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
            expectedResult: (): null => {
                return null;
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const postId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];
        const media = seededObjects.medias[0];
        const feedback = seededObjects.feedbacks[0];

        assert(media.type === MediaType.PHOTO);

        const update: SocialPostDto = {
            id: postId,
            title: 'New title',
            text: 'New text',
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
            postType: PostType.VIDEO,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            attachments: [
                {
                    id: media._id.toString(),
                    type: media.type,
                    aiDescription: media.aiDescription,
                    transformData: {
                        aspectRatio: media.transformData!.aspectRatio!,
                        rotationInDegrees: media.transformData!.rotationInDegrees!,
                        left: media.transformData!.left!,
                        top: media.transformData!.top!,
                        width: media.transformData!.width!,
                        height: media.transformData!.height!,
                    },
                    thumbnail1024OutsideUrl: media.storedObjects!.thumbnail1024Outside!.publicUrl!,
                    thumbnail256OutsideUrl: media.storedObjects!.thumbnail256Outside!.publicUrl!,
                    aspectRatio: media.aspectRatio!,
                },
            ],
            hashtags: {
                selected: [
                    {
                        id: newDbId().toString(),
                        text: 'hashtag1',
                        isCustomerInput: false,
                        isMain: true,
                        type: HashtagType.RESTAURANT,
                    },
                ],
                suggested: [
                    {
                        id: newDbId().toString(),
                        text: 'hashtag1',
                        isCustomerInput: false,
                        isMain: true,
                        type: HashtagType.RESTAURANT,
                    },
                    {
                        id: newDbId().toString(),
                        text: 'hashtag2',
                        isCustomerInput: true,
                        isMain: false,
                        type: HashtagType.RESTAURANT,
                    },
                ],
            },
            socialCreatedAt: undefined,
            socialLink: null,
            location: null,
            feedbacks: {
                id: feedback._id.toString(),
                isOpen: !!feedback.isOpen,
                participants: [],
                feedbackMessages: [],
                createdAt: feedback.createdAt!.toISOString(),
                updatedAt: feedback.updatedAt!.toISOString(),
            },
            callToAction: undefined,
            error: undefined,
            author: undefined,
            userTagsList: [],
            tiktokOptions: {
                privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                interactionAbility: {
                    comment: false,
                    duet: false,
                    stitch: false,
                },
                contentDisclosureSettings: {
                    isActivated: false,
                    yourBrand: false,
                    brandedContent: false,
                },
                autoAddMusic: false,
            },
            reelThumbnailFromMedia: undefined,
            reelThumbnailFromFrame: undefined,
            instagramCollaboratorsUsernames: [],
            createdFromDeviceType: undefined,
        };

        const post = await updateSocialPostUseCase.execute({
            post: update,
            author: {
                id: user._id.toString(),
                name: user.name,
                lastname: user.lastname,
            },
        });

        expect(omit(post, 'attachments', 'hashtags', 'feedbacks', 'bindingId', 'socialLink', 'socialCreatedAt')).toStrictEqual(
            omit(update, 'attachments', 'hashtags', 'feedbacks', 'bindingId', 'socialLink', 'socialCreatedAt')
        );
        expect(post.attachments.map((attachment) => attachment.id)).toIncludeSameMembers(
            update.attachments.map((attachment) => attachment.id)
        );
        expect(post.hashtags?.selected?.map((selected) => omit(selected, 'id', 'createdAt', 'updatedAt')) ?? []).toIncludeSameMembers(
            update.hashtags?.selected?.map((selected) => omit(selected, 'id', 'createdAt', 'updatedAt')) ?? []
        );
        expect(post.hashtags?.suggested?.map((suggested) => omit(suggested, 'id', 'createdAt', 'updatedAt')) ?? []).toIncludeSameMembers(
            update.hashtags?.suggested?.map((suggested) => omit(suggested, 'id', 'createdAt', 'updatedAt')) ?? []
        );
        expect(post.feedbacks?.id).toEqual(update.feedbacks?.id);
    });

    it("should create the authors array when it doesn't exist yet", async () => {
        const updateSocialPostUseCase = container.resolve(UpdateSocialPostUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'medias' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).source(PostSource.SOCIAL).build()];
                    },
                },
                medias: {
                    data(dependencies) {
                        return [
                            getDefaultMedia()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .transformData({
                                    aspectRatio: AspectRatio.ORIGINAL,
                                    width: 200,
                                    height: 200,
                                    rotationInDegrees: 0,
                                    left: 0,
                                    top: 0,
                                })
                                .aspectRatio(1)
                                .storedObjects({
                                    thumbnail1024Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail1024Outside',
                                        provider: 'S3',
                                    },
                                    thumbnail256Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail256Outside',
                                        provider: 'S3',
                                    },
                                    original: { key: 'key', publicUrl: 'http://www.url.com/original', provider: 'S3' },
                                    normalized: { key: 'key', publicUrl: 'http://www.url.com/normalized', provider: 'S3' },
                                })
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
            expectedResult: (dependencies): IPost['authors'] => {
                return [
                    {
                        _id: dependencies.users[0]._id,
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname,
                        picture: null,
                    },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const postId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];
        const media = seededObjects.medias[0];
        const feedback = seededObjects.feedbacks[0];

        assert(media.type === MediaType.PHOTO);

        const update: SocialPostDto = {
            id: postId,
            title: 'New title',
            text: 'New text',
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
            postType: PostType.VIDEO,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            attachments: [
                {
                    id: media._id.toString(),
                    type: media.type,
                    aiDescription: media.aiDescription,
                    transformData: {
                        aspectRatio: media.transformData!.aspectRatio!,
                        rotationInDegrees: media.transformData!.rotationInDegrees!,
                        left: media.transformData!.left!,
                        top: media.transformData!.top!,
                        width: media.transformData!.width!,
                        height: media.transformData!.height!,
                    },
                    thumbnail1024OutsideUrl: media.storedObjects!.thumbnail1024Outside!.publicUrl!,
                    thumbnail256OutsideUrl: media.storedObjects!.thumbnail256Outside!.publicUrl!,
                    aspectRatio: media.aspectRatio!,
                },
            ],
            hashtags: {
                selected: [
                    {
                        id: newDbId().toString(),
                        text: 'hashtag1',
                        isCustomerInput: false,
                        isMain: true,
                        type: HashtagType.RESTAURANT,
                    },
                ],
                suggested: [
                    {
                        id: newDbId().toString(),
                        text: 'hashtag1',
                        isCustomerInput: false,
                        isMain: true,
                        type: HashtagType.RESTAURANT,
                    },
                    {
                        id: newDbId().toString(),
                        text: 'hashtag2',
                        isCustomerInput: true,
                        isMain: false,
                        type: HashtagType.RESTAURANT,
                    },
                ],
            },
            socialCreatedAt: undefined,
            socialLink: null,
            location: null,
            feedbacks: {
                id: feedback._id.toString(),
                isOpen: !!feedback.isOpen,
                participants: [],
                feedbackMessages: [],
                createdAt: feedback.createdAt!.toISOString(),
                updatedAt: feedback.updatedAt!.toISOString(),
            },
            callToAction: undefined,
            error: undefined,
            userTagsList: [],
            tiktokOptions: {
                privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                interactionAbility: {
                    comment: false,
                    duet: false,
                    stitch: false,
                },
                contentDisclosureSettings: {
                    isActivated: false,
                    yourBrand: false,
                    brandedContent: false,
                },
                autoAddMusic: false,
            },
        };

        const author = {
            id: user._id.toString(),
            name: user.name,
            lastname: user.lastname,
        };

        const expectedResult = testCase.getExpectedResult();

        await updateSocialPostUseCase.execute({
            post: update,
            author,
        });

        const postsRepository = container.resolve(PostsRepository);
        const updatedPost = await postsRepository.findOne({ filter: { _id: toDbId(postId) }, options: { lean: true } });

        expect(updatedPost?.authors).toEqual(expectedResult);
    });

    it('should add the author to the existing authors array', async () => {
        const updateSocialPostUseCase = container.resolve(UpdateSocialPostUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'medias' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [
                            getDefaultUser().name('user_0').lastname('user_0_lastname').email('<EMAIL>').build(),
                            getDefaultUser().name('user_1').lastname('user_1_lastname').email('<EMAIL>').build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[1]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .authors([
                                    {
                                        _id: dependencies.users()[1]._id,
                                        name: dependencies.users()[1].name,
                                        lastname: dependencies.users()[1].lastname,
                                        picture: null,
                                    },
                                ])
                                .build(),
                        ];
                    },
                },
                medias: {
                    data(dependencies) {
                        return [
                            getDefaultMedia()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .transformData({
                                    aspectRatio: AspectRatio.ORIGINAL,
                                    width: 200,
                                    height: 200,
                                    rotationInDegrees: 0,
                                    left: 0,
                                    top: 0,
                                })
                                .aspectRatio(1)
                                .storedObjects({
                                    thumbnail1024Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail1024Outside',
                                        provider: 'S3',
                                    },
                                    thumbnail256Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail256Outside',
                                        provider: 'S3',
                                    },
                                    original: { key: 'key', publicUrl: 'http://www.url.com/original', provider: 'S3' },
                                    normalized: { key: 'key', publicUrl: 'http://www.url.com/normalized', provider: 'S3' },
                                })
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
            expectedResult: (dependencies): IPost['authors'] => {
                return [
                    {
                        _id: dependencies.users[1]._id,
                        name: dependencies.users[1].name,
                        lastname: dependencies.users[1].lastname,
                        picture: null,
                    },
                    {
                        _id: dependencies.users[0]._id,
                        name: dependencies.users[0].name,
                        lastname: dependencies.users[0].lastname,
                        picture: null,
                    },
                ];
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const postId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];
        const media = seededObjects.medias[0];
        const feedback = seededObjects.feedbacks[0];

        assert(media.type === MediaType.PHOTO);

        const update: SocialPostDto = {
            id: postId,
            title: 'New title',
            text: 'New text',
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
            postType: PostType.VIDEO,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            attachments: [
                {
                    id: media._id.toString(),
                    type: media.type,
                    aiDescription: media.aiDescription,
                    transformData: {
                        aspectRatio: media.transformData!.aspectRatio!,
                        rotationInDegrees: media.transformData!.rotationInDegrees!,
                        left: media.transformData!.left!,
                        top: media.transformData!.top!,
                        width: media.transformData!.width!,
                        height: media.transformData!.height!,
                    },
                    thumbnail1024OutsideUrl: media.storedObjects!.thumbnail1024Outside!.publicUrl!,
                    thumbnail256OutsideUrl: media.storedObjects!.thumbnail256Outside!.publicUrl!,
                    aspectRatio: media.aspectRatio!,
                },
            ],
            hashtags: {
                selected: [
                    {
                        id: newDbId().toString(),
                        text: 'hashtag1',
                        isCustomerInput: false,
                        isMain: true,
                        type: HashtagType.RESTAURANT,
                    },
                ],
                suggested: [
                    {
                        id: newDbId().toString(),
                        text: 'hashtag1',
                        isCustomerInput: false,
                        isMain: true,
                        type: HashtagType.RESTAURANT,
                    },
                    {
                        id: newDbId().toString(),
                        text: 'hashtag2',
                        isCustomerInput: true,
                        isMain: false,
                        type: HashtagType.RESTAURANT,
                    },
                ],
            },
            socialCreatedAt: undefined,
            socialLink: null,
            location: null,
            feedbacks: {
                id: feedback._id.toString(),
                isOpen: !!feedback.isOpen,
                participants: [],
                feedbackMessages: [],
                createdAt: feedback.createdAt!.toISOString(),
                updatedAt: feedback.updatedAt!.toISOString(),
            },
            callToAction: undefined,
            error: undefined,
            userTagsList: [],
            tiktokOptions: {
                privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                interactionAbility: {
                    comment: false,
                    duet: false,
                    stitch: false,
                },
                contentDisclosureSettings: {
                    isActivated: false,
                    yourBrand: false,
                    brandedContent: false,
                },
                autoAddMusic: false,
            },
        };

        const author = {
            id: user._id.toString(),
            name: user.name,
            lastname: user.lastname,
        };

        const expectedResult = testCase.getExpectedResult();

        await updateSocialPostUseCase.execute({
            post: update,
            author,
        });

        const postsRepository = container.resolve(PostsRepository);
        const updatedPost = await postsRepository.findOne({ filter: { _id: toDbId(postId) }, options: { lean: true } });

        expect(updatedPost?.authors).toEqual(expectedResult);
    });

    it('should keep createdFromDeviceType untouched when updating a post', async () => {
        const updateSocialPostUseCase = container.resolve(UpdateSocialPostUseCase);
        const postsRepository = container.resolve(PostsRepository);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants' | 'posts' | 'medias' | 'feedbacks'>({
            seeds: {
                restaurants: {
                    data() {
                        return [getDefaultRestaurant().name('restaurant_0').build()];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                        ];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [
                            getDefaultPost()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .source(PostSource.SOCIAL)
                                .createdFromDeviceType(DeviceType.DESKTOP)
                                .build(),
                        ];
                    },
                },
                medias: {
                    data(dependencies) {
                        return [
                            getDefaultMedia()
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .transformData({
                                    aspectRatio: AspectRatio.ORIGINAL,
                                    width: 200,
                                    height: 200,
                                    rotationInDegrees: 0,
                                    left: 0,
                                    top: 0,
                                })
                                .aspectRatio(1)
                                .storedObjects({
                                    thumbnail1024Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail1024Outside',
                                        provider: 'S3',
                                    },
                                    thumbnail256Outside: {
                                        key: 'key',
                                        publicUrl: 'http://www.url.com/thumbnail256Outside',
                                        provider: 'S3',
                                    },
                                    original: { key: 'key', publicUrl: 'http://www.url.com/original', provider: 'S3' },
                                    normalized: { key: 'key', publicUrl: 'http://www.url.com/normalized', provider: 'S3' },
                                })
                                .build(),
                        ];
                    },
                },
                feedbacks: {
                    data() {
                        return [getDefaultFeedback().build()];
                    },
                },
            },
        });

        await testCase.build();

        const seededObjects = testCase.getSeededObjects();
        const postId = seededObjects.posts[0]._id.toString();
        const user = seededObjects.users[0];
        const media = seededObjects.medias[0];
        const feedback = seededObjects.feedbacks[0];

        assert(media.type === MediaType.PHOTO);

        const update: SocialPostDto = {
            id: postId,
            title: 'New title',
            text: 'New text',
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            platformKeys: [PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
            postType: PostType.VIDEO,
            plannedPublicationDate: DateTime.now().plus({ days: 1 }).toJSDate().toISOString(),
            attachments: [
                {
                    id: media._id.toString(),
                    type: media.type,
                    aiDescription: media.aiDescription,
                    transformData: {
                        aspectRatio: media.transformData!.aspectRatio!,
                        rotationInDegrees: media.transformData!.rotationInDegrees!,
                        left: media.transformData!.left!,
                        top: media.transformData!.top!,
                        width: media.transformData!.width!,
                        height: media.transformData!.height!,
                    },
                    thumbnail1024OutsideUrl: media.storedObjects!.thumbnail1024Outside!.publicUrl!,
                    thumbnail256OutsideUrl: media.storedObjects!.thumbnail256Outside!.publicUrl!,
                    aspectRatio: media.aspectRatio!,
                },
            ],
            hashtags: {
                selected: [
                    {
                        id: newDbId().toString(),
                        text: 'hashtag1',
                        isCustomerInput: false,
                        isMain: true,
                        type: HashtagType.RESTAURANT,
                    },
                ],
                suggested: [
                    {
                        id: newDbId().toString(),
                        text: 'hashtag1',
                        isCustomerInput: false,
                        isMain: true,
                        type: HashtagType.RESTAURANT,
                    },
                ],
            },
            socialCreatedAt: undefined,
            socialLink: null,
            location: null,
            feedbacks: {
                id: feedback._id.toString(),
                isOpen: !!feedback.isOpen,
                participants: [],
                feedbackMessages: [],
                createdAt: feedback.createdAt!.toISOString(),
                updatedAt: feedback.updatedAt!.toISOString(),
            },
            callToAction: undefined,
            error: undefined,
            userTagsList: [],
            tiktokOptions: {
                privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                interactionAbility: {
                    comment: false,
                    duet: false,
                    stitch: false,
                },
                contentDisclosureSettings: {
                    isActivated: false,
                    yourBrand: false,
                    brandedContent: false,
                },
                autoAddMusic: false,
            },
            createdFromDeviceType: DeviceType.MOBILE,
        };

        await updateSocialPostUseCase.execute({
            post: update,
            author: { id: user._id.toString(), name: user.name, lastname: user.lastname },
        });

        const stored = await postsRepository.findOne({ filter: { _id: toDbId(postId) }, options: { lean: true } });
        expect(stored?.createdFromDeviceType).toBe(DeviceType.DESKTOP);
    });
});
