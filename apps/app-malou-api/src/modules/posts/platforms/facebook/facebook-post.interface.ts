/* eslint-disable no-use-before-define */
/* eslint-disable camelcase */
import { ISocialAttachment } from '@malou-io/package-models';
import { PostType } from '@malou-io/package-utils';

export interface Image {
    height?: number;
    src?: string;
    width?: number;
}
export interface Media {
    image?: Image;
    source?: string;
}

export interface AttachmentsDatum {
    media?: Media;
    media_type?: string;
    type?: string;
    subattachments?: Attachments;
    target?: {
        id?: string;
    };
}

export interface Attachments {
    data?: AttachmentsDatum[];
}

export interface CommentsDatum {
    like_count?: number;
    id?: string;
    from?: DatumFrom;
    message?: string;
    created_time?: string;
    comments?: Comments;
}

export interface Comments {
    data?: CommentsDatum[];
    summary?: Summary;
}

export interface DatumFrom {
    name?: string;
    id?: string;
}

export interface PictureData {
    height?: number;
    is_silhouette?: boolean;
    url?: string;
    width?: number;
}

export interface Picture {
    data?: PictureData;
}

interface Likes {
    summary?: Summary;
}

interface Summary {
    total_count?: number;
}

interface Insights {
    data?: InsightData[];
}

interface InsightData {
    name?: InsightType;
    values?: Value[];
}

export enum InsightType {
    POST_IMPRESSIONS = 'post_impressions',
}

interface Value {
    value?: number;
}

export interface PostDataFrom {
    name?: string;
    id?: string;
    picture?: Picture;
}

export enum PostMediaPrivacyValue {
    SELF = 'SELF',
}

export interface FbPostMediaData {
    id?: string;
    from?: PostDataFrom;
    created_time?: string;
    updated_time?: string;
    published?: boolean;
    privacy?: { value: PostMediaPrivacyValue };
    post_id?: string;
}

export interface FbPostData {
    id?: string;
    from?: PostDataFrom;
    message?: string;
    is_published?: boolean;
    story?: string;
    created_time?: string;
    updated_time?: string;
    status_type?: string;
    permalink_url?: string;
    attachments?: Attachments;
    comments?: Comments;
    username?: string;
    likes?: Likes;
    insights?: Insights;
    shares?: { count: number };
}

export interface FbPostInsight {
    postType: PostType;
    socialId: string;
    username: string;
    permalink: string;
    caption: string;
    createdAt: Date;
    shares?: number;
    impressions: number;
    likes: number;
    comments: number;
    plays?: number;
    url: string;
    carouselUrls: { url: string; type: string }[];
    socialAttachments: ISocialAttachment[];
    thumbnail?: string;
}

export enum FacebookPostType {
    VIDEO = 'video',
    PHOTOS = 'photos',
    REEL = 'reel',
}
