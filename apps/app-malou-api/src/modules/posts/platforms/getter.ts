import { singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { FacebookPostsUseCases } from ':modules/posts/platforms/facebook/use-cases';
import { GmbPostsUseCases } from ':modules/posts/platforms/gmb/use-cases';
import { InstagramPostsUseCases } from ':modules/posts/platforms/instagram/use-cases';
import { MapstrPostsUseCases } from ':modules/posts/platforms/mapstr/mapstr-post.use-cases';
import { TiktokPostsUseCases } from ':modules/posts/platforms/tiktok/tiktok-post.use-cases';
import { PlatformPostUseCases } from ':modules/posts/posts.interface';

@singleton()
export class PlatformPostGetter {
    constructor(
        private readonly _instagramPostsUseCases: InstagramPostsUseCases,
        private readonly _facebookPostsUseCases: FacebookPostsUseCases,
        private readonly _mapstrPostsUseCases: MapstrPostsUseCases,
        private readonly _gmbPostsUseCases: GmbPostsUseCases,
        private readonly _tiktokPostsUseCases: TiktokPostsUseCases
    ) {}

    getPlatformPostUseCases(key: PlatformKey): PlatformPostUseCases {
        const platformUseCases: Partial<Record<PlatformKey, PlatformPostUseCases>> = {
            [PlatformKey.FACEBOOK]: this._facebookPostsUseCases,
            [PlatformKey.GMB]: this._gmbPostsUseCases,
            [PlatformKey.INSTAGRAM]: this._instagramPostsUseCases,
            [PlatformKey.MAPSTR]: this._mapstrPostsUseCases,
            [PlatformKey.TIKTOK]: this._tiktokPostsUseCases,
        };

        const platformUseCase: PlatformPostUseCases | undefined = platformUseCases[key];

        if (!platformUseCase) {
            throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
                metadata: {
                    platformKey: key,
                },
            });
        }

        return platformUseCase;
    }
}
