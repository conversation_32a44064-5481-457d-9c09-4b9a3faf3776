import { singleton } from 'tsyringe';

import { IPopulatedPost, IPost, IPostWithAttachments, IPostWithAttachmentsAndThumbnail, toDbId } from '@malou-io/package-models';
import { isBetween, MalouErrorCode, MediaType, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightFiltersApi } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsight } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { TiktokPostMapper } from ':modules/posts/platforms/tiktok/tiktok-post-mapper';
import { TiktokPostInitPublishUseCase } from ':modules/posts/platforms/tiktok/use-cases/tiktok-post-init-publish.use-case';
import { MalouPostData, PlatformPostUseCases } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';

@singleton()
export class TiktokPostsUseCases implements PlatformPostUseCases {
    constructor(
        private _tiktokPostInitPublishUseCase: TiktokPostInitPublishUseCase,
        private _platformsRepository: PlatformsRepository,
        private _callTiktokApiService: CallTiktokApiService,
        private _tiktokProvider: TiktokProvider,
        private _postsRepository: PostsRepository
    ) {}

    async updatePost(post: IPost): Promise<IPost> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_UPDATE_POST] Cant update Tiktok post when already published');
        return post;
    }

    async deletePost(post: IPost): Promise<IPost> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_DELETE_POST] Cant delete Tiktok post when already published');
        return post;
    }

    async synchronizeStories(): Promise<MalouPostData[]> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_SYNCHRONIZE_STORIES] TikTok does not support stories');
        return [];
    }

    async fetchPost(_postId: string): Promise<MalouPostData | null> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_FETCH_POST] TikTok does not support fetching individual posts');
        return null;
    }

    async completePublish(_post: IPopulatedPost): Promise<void> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_COMPLETE_PUBLISH] TikTok does not support completing publish');
    }

    async getCompetitorsPosts(): Promise<MalouPostData[]> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_GET_COMPETITORS_POSTS] TikTok does not support fetching competitors posts');
        return [];
    }

    async publishStory(_platformId: string, _creationId: string, _type: MediaType, postId: string): Promise<IPost> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_PUBLISH_STORY] TikTok does not support publishing stories');
        return this._postsRepository.findOneOrFail({ filter: { _id: toDbId(postId) } });
    }

    async createStoryList(): Promise<void> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_CREATE_STORY_LIST] TikTok does not support creating story lists');
    }

    async upsertStoryAndSaveAttachments(): Promise<void> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_UPSERT_STORY_AND_SAVE_ATTACHMENTS] TikTok does not support upserting stories');
    }

    async searchAccounts(): Promise<void> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_SEARCH_ACCOUNTS] TikTok does not support searching accounts');
    }

    async oembed(): Promise<void> {
        logger.warn('[TIKTOK_POSTS_USE_CASE_OEMBED] TikTok does not support oEmbed');
    }

    async synchronize({ platform }: { platform: Platform }): Promise<MalouPostData[]> {
        const credentials = platform.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, {
                message: 'Credentials not found in tiktok synchronize use case',
            });
        }
        const postMapper = new TiktokPostMapper();
        const {
            data: { videos },
        } = await this._callTiktokApiService.execute({
            credentialId: credentialId.toString(),
            method: this._tiktokProvider.getAllVideos,
            args: {},
        });

        if (!videos.length) {
            return [];
        }
        const mappedPosts = videos.map((p) => postMapper.mapToMalouPost({ post: p, platform }));
        return mappedPosts;
    }

    async publish({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }): Promise<void> {
        await this._tiktokPostInitPublishUseCase.execute({ post: post as IPopulatedPost });
    }

    async fetchPostsWithInsights(
        restaurantId: string,
        tiktokFilters: PlatformInsightFiltersApi,
        _shouldRaiseError?: boolean
    ): Promise<PostInsight[]> {
        const { startDate, endDate } = tiktokFilters;
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.TIKTOK);
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: { restaurantId, key: PlatformKey.TIKTOK },
            });
        }

        const { data } = await this._callTiktokApiService.execute({
            restaurantId: restaurantId.toString(),
            method: this._tiktokProvider.getAllVideos,
            args: {},
        });

        return data.videos
            .filter((post) => !!post)
            .map((post) => new TiktokPostMapper().mapToInsightPost(post))
            .filter((post) => isBetween(post.createdAt, startDate, endDate));
    }
}
