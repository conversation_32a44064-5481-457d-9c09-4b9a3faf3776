import { toDbId } from '@malou-io/package-models';
import {
    createDate,
    MalouErrorCode,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
    roundToDecimals,
    SocialAttachmentsMediaTypes,
    TimeInMilliseconds,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Platform } from ':modules/platforms/platforms.entity';
import { PostInsight, PostInsightWithStats } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { TiktokPostData } from ':modules/posts/platforms/tiktok/tiktok-post.interface';
import { MalouPostData } from ':modules/posts/posts.interface';
import { PostMapper } from ':modules/posts/posts.mapper';

export class TiktokPostMapper extends PostMapper {
    constructor() {
        super();
    }

    mapToMalouPost({ post, platform }: { post: TiktokPostData; platform: Platform }): MalouPostData {
        if (!platform) {
            throw new MalouError(MalouErrorCode.INVALID_DATA, {
                metadata: {
                    post,
                    platform: PlatformKey.TIKTOK,
                },
            });
        }

        const isReel = post.duration > 0;
        const res: MalouPostData = {
            socialId: post.id,
            socialCreatedAt: post.create_time ? (createDate(post.create_time * TimeInMilliseconds.SECOND) ?? new Date()) : new Date(),
            sortDate: post.create_time ? (createDate(post.create_time * TimeInMilliseconds.SECOND) ?? new Date()) : new Date(),
            socialUpdatedAt: post.create_time ? (createDate(post.create_time * TimeInMilliseconds.SECOND) ?? new Date()) : new Date(),
            socialLink: post.share_url,
            title: post.title,
            text: post.video_description ?? '',
            published: PostPublicationStatus.PUBLISHED,
            key: PlatformKey.TIKTOK,
            source: PostSource.SOCIAL,
            postType: isReel ? PostType.REEL : PostType.CAROUSEL,
            isReelDisplayedInFeed: true,
            restaurantId: toDbId(platform.restaurantId),
            platformId: toDbId(platform._id),
            socialAttachments: [
                {
                    type: SocialAttachmentsMediaTypes.IMAGE,
                    urls: { original: post.cover_image_url },
                },
            ],
        };
        return res;
    }

    mapToInsightPost(post: TiktokPostData): PostInsight {
        const isReel = post.duration > 0;
        return {
            postType: isReel ? PostType.REEL : PostType.CAROUSEL,
            username: '', // TikTok does not provide username in video data
            socialId: post.id,
            permalink: post.share_url,
            caption: post.video_description,
            createdAt: post.create_time ? (createDate(post.create_time * TimeInMilliseconds.SECOND) ?? new Date()) : new Date(),
            impressions: isReel ? 0 : post.view_count,
            reach: 0, // TikTok does not provide reach
            plays: isReel ? post.view_count : 0,
            likes: post.like_count,
            comments: post.comment_count,
            saved: 0, // TikTok does not provide saved
            shares: post.share_count,
            url: post.cover_image_url,
            thumbnail: isReel ? post.cover_image_url : undefined,
            thumbnailUrl: post.cover_image_url,
            carouselUrls: isReel
                ? []
                : [
                      {
                          url: post.cover_image_url,
                          type: 'image',
                      },
                  ],
        };
    }

    mapToPostInsightsStatistics(postInsight: PostInsight, followersOnCreationTimeCount?: number): PostInsightWithStats {
        const impressions = (postInsight.postType === PostType.REEL ? postInsight.plays : postInsight.impressions) ?? 0;
        const interactions = (postInsight.comments ?? 0) + (postInsight.likes ?? 0) + (postInsight.saved ?? 0) + (postInsight.shares ?? 0);
        const engagementRate =
            followersOnCreationTimeCount === null || followersOnCreationTimeCount === undefined || followersOnCreationTimeCount === 0
                ? undefined
                : roundToDecimals((interactions / followersOnCreationTimeCount) * 100, 2);

        return {
            ...postInsight,
            stats: {
                impressions,
                interactions,
                engagementRate,
            },
        };
    }
}
