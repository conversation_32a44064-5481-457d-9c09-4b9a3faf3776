import { FilterQuery } from 'mongoose';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import {
    DbId,
    ID,
    IPlatform,
    IPost,
    IPostWithAttachments,
    IPostWithAttachmentsAndThumbnail,
    IRestaurant,
    newDbId,
    toDbId,
} from '@malou-io/package-models';
import {
    EmailCategory,
    EmailType,
    getFileExtension,
    getFileFormatFromExtension,
    isBetween,
    MalouErrorCode,
    MediaCategory,
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    PostType,
    TimeInMilliseconds,
    TimeInSeconds,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightFiltersInstagram } from ':helpers/filters/platform-insight-filters-api-factory';
import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import { FacebookErrorCode, isFacebookError } from ':helpers/utils';
import { IgMediaContainerStatus } from ':modules/credentials/platforms/facebook/facebook.types';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { isFbTimeoutError } from ':modules/credentials/platforms/facebook/facebook.use-cases';
import FeedbacksRepository from ':modules/feedbacks/feedback.repository';
import MailingUseCases from ':modules/mailing/use-cases';
import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { MediaUploaderService } from ':modules/media/services/media-uploader/media-uploader.service';
import { CreatePostErrorNotificationProducer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.producer';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { UpsertPlatformPostInsightsService } from ':modules/post-insights/v2/services/upsert-platform-post-insights/upsert-platform-post-insights.service';
import { MalouPostData, PlatformPostUseCases, StoryToPublish } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import { sendCompletePublishPost } from ':modules/posts/queues/publish-post/publish-post.producer';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { Cache } from ':plugins/cache';
import { SlackChannel, SlackService } from ':services/slack.service';

import { InstagramPostMapper } from './instagram-post-mapper';
import { IgPostData, PostInsight } from './instagram-post.interface';
import { INSTAGRAM_NOT_SUPPORTED_FORMAT_ERROR } from './instagram.error';

@singleton()
export class InstagramPostsUseCases implements PlatformPostUseCases {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _slackService: SlackService,
        private readonly _mailingUseCases: MailingUseCases,
        private readonly _feedbacksRepository: FeedbacksRepository,
        private readonly _mediaUploaderService: MediaUploaderService,
        private readonly _mediasRepository: MediasRepository,
        private readonly _createPostErrorNotificationProducer: CreatePostErrorNotificationProducer,
        private readonly _upsertPlatformPostInsightsService: UpsertPlatformPostInsightsService,
        @inject(InjectionToken.Cache) private readonly _cache: Cache
    ) {}

    mapPostsDataToMalou(posts: IgPostData[], platform: Platform) {
        const igPostMapper = new InstagramPostMapper();
        return posts?.map((post) => igPostMapper.mapToMalouPost({ post, platform }));
    }

    mapPostDataToMalou(igPostData: IgPostData, platform: Platform) {
        const igPostMapper = new InstagramPostMapper();
        return igPostMapper.mapToMalouPost({ post: igPostData, platform });
    }

    async synchronize({ platform, recentPostsOnly }: { platform: Platform; recentPostsOnly: boolean }): Promise<MalouPostData[]> {
        assert(platform.socialId, 'Missing socialId on platform');
        const credentials = platform.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, {
                message: 'Credentials not found in instagram synchronize use case',
            });
        }
        const postMapper = new InstagramPostMapper();
        const IG_RECENT_POSTS_FETCH_COUNT = 20;
        const maxPostCount = recentPostsOnly ? IG_RECENT_POSTS_FETCH_COUNT : Infinity;
        const result = await facebookCredentialsUseCases.igGetPagePosts(credentialId, platform.socialId, maxPostCount);
        this._upsertPlatformPostInsightsService
            .upsertIgPostInsights({
                posts: result.media.data,
                platformSocialId: platform.socialId,
            })
            .catch((error) => {
                logger.error('[InstagramPostsUseCases][synchronize] Error while upserting post insights', {
                    error,
                    platformSocialId: platform.socialId,
                });
            });
        const igPosts = result.media.data;
        if (!igPosts) {
            return [];
        }
        const mappedPosts = igPosts.map((p) => postMapper.mapToMalouPost({ post: p, platform }));
        return mappedPosts;
    }

    async fetchPost({ post }: { post: IPost }): Promise<IPost> {
        assert(post.platformId, 'Missing platformId on post');
        assert(post.socialId, 'Missing socialId on post');
        const postMapper = new InstagramPostMapper();
        const platform = await this._platformsRepository.getPlatformById(post.platformId.toString());

        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                message: 'Platform not found in instagram fetch post use case',
                metadata: { platformId: post.platformId },
            });
        }
        const credentials = platform.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(platform.socialId, 'Missing socialId on platform');
        const igPost = await facebookCredentialsUseCases.igGetPost(credentialId, post.socialId, platform.socialId);
        return postMapper.mapToMalouPost({ post: igPost, platform }) as IPost;
    }

    async publish({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }): Promise<void> {
        if (!post.platformId) {
            const malouError = new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                message: 'Platform not found in instagram publish post use case',
            });
            await this._sendAlertForPublishIgPost(post, new Error(malouError.message), malouError.malouErrorCode, malouError.message);
            throw malouError;
        }

        const platform = await this._platformsRepository.getPlatformById(post.platformId.toString());

        if (!platform) {
            const malouError = new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                message: 'Platform not found in instagram publish post use case',
            });
            await this._sendAlertForPublishIgPost(post, new Error(malouError.message), malouError.malouErrorCode, malouError.message);
            throw malouError;
        }
        const credentials = platform.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            const malouError = new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
            await this._sendAlertForPublishIgPost(
                post,
                new Error(malouError.message),
                malouError.malouErrorCode,
                `Credentials for platform ${platform._id} not found`
            );
            throw malouError;
        }
        assert(platform.socialId, 'Missing socialId on platform');
        let creationId: string;
        try {
            creationId = await this._getCreationId(post, credentialId, platform.socialId);
        } catch (error: any) {
            if (error.error_user_title?.match(/Échec du traitement de l’ID de lieu fourni/)) {
                try {
                    // Remove location data and retry to post
                    await this._postsRepository.updateOne({
                        filter: { _id: post._id },
                        update: { location: null },
                    });
                    post.location = null;

                    creationId = await this._getCreationId(post, credentialId, platform.socialId);

                    await this._sendExpiredLocationEmail(post._id);
                } catch (err) {
                    await this._handlePublishError(post._id, err);
                    return;
                }
            } else {
                await this._handlePublishError(post._id, error);
                return;
            }
        }
        await sendCompletePublishPost({
            creationId,
            postId: post._id,
        });
    }

    private async _getCreationId(post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments, credentialId: string, socialId: string) {
        switch (post.postType) {
            case PostType.CAROUSEL:
                return this._createCarousel(post, credentialId, socialId);
            default:
                return this._createSingleMediaPost(post, credentialId, socialId);
        }
    }

    private async _createCarousel(post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments, credentialId: string, socialId: string) {
        // create a post list with only attachments length = 1
        const posts = post.attachments.map((attachment, idx) => ({
            ...post,
            attachments: [attachment],
            userTags: post.userTagsList?.[idx] || [],
        }));
        const AVOID_APPLICATION_REACHED_REQUEST_LIMIT_MEDIA_THRESHOLD = 5; // Sometimes calling the API with several media at the same time raises an anti spam error https://developers.facebook.com/support/bugs/624079875915180/?join_id=f7fb6f1d0ce3e2
        let creationIds: any[] = [];
        if (posts?.length < AVOID_APPLICATION_REACHED_REQUEST_LIMIT_MEDIA_THRESHOLD) {
            creationIds = await Promise.all(
                posts.map((mappedPost) =>
                    facebookCredentialsUseCases.igCreatePost(credentialId, mappedPost, socialId, true).then((result) => result.id)
                )
            );
        } else {
            for (const mappedPost of posts) {
                const result = await facebookCredentialsUseCases.igCreatePost(credentialId, mappedPost, socialId, true);
                creationIds.push(result.id);
                await waitFor(500);
            }
        }

        const MAX_TRIES = 30;

        // need to loop to check when media are imported
        // but ! For carousel only (idk why), even all media are imported (all status FINISHED), the creationId is not ready
        // so to "retry", it's handled by a "setTimeout" in function "completePublish"
        for (let i = 0; i < MAX_TRIES; i++) {
            const statuses = await Promise.all(
                creationIds.map((id) => facebookCredentialsUseCases.igCheckCreationStatus(credentialId, id))
            );

            if (!statuses.map((s) => s.status_code).some((s) => s === IgMediaContainerStatus.IN_PROGRESS)) {
                if (statuses.map((s) => s.status_code).every((s) => s === IgMediaContainerStatus.FINISHED)) {
                    break;
                }
                if (statuses.map((s) => s.status_code).some((s) => s === IgMediaContainerStatus.ERROR)) {
                    const fbErrs = statuses.map((s) => {
                        if (s.status_code === IgMediaContainerStatus.ERROR) {
                            if (this._isInvalidMedia(s)) {
                                logger.error('[IG_MEDIA_NOT_SUPPORTED_FORMAT_ERROR] Carousel', { status: s, socialId, credentialId });
                                return 'Invalid media';
                            }
                            return s.status;
                        }
                        return null;
                    });
                    throw new Error(
                        `${fbErrs
                            .map((e, idx) => (e ? `${idx + 1}::${e}` : null))
                            .filter((e) => e)
                            .join('__')}`
                    );
                    // 1::Invalid media__2::Other error__4::Invalid media
                }
            }
            await waitFor(3000);
        }

        const { id } = await facebookCredentialsUseCases.igCreateCarousel(credentialId, creationIds, post, socialId);
        return id;
    }

    private async _createSingleMediaPost(
        post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments,
        credentialId: string,
        socialId: string
    ) {
        const mappedPost = {
            ...post,
            userTags: post.userTagsList?.[0] ?? [],
        };
        const { id } = await facebookCredentialsUseCases.igCreatePost(credentialId, mappedPost, socialId);
        const MAX_TRIES = 30;

        // need to loop to check when medium is imported and to handle a weird bug:
        // the creationId is not ready and we throw [IG_MEDIA_NOT_READY] error and we set the post as "ERROR"
        // BUT !!! The post is published anyway on the platform
        // So it's better to check status and if it's "FINISHED" this bug disappear ... (awesome facebook api thx u ^^)
        for (let i = 0; i < MAX_TRIES; i++) {
            const status = await facebookCredentialsUseCases.igCheckCreationStatus(credentialId, id);
            if (status.status_code === IgMediaContainerStatus.FINISHED) {
                break;
            }
            if (status.status_code === IgMediaContainerStatus.ERROR) {
                if (this._isInvalidMedia(status)) {
                    logger.error('[IG_MEDIA_NOT_SUPPORTED_FORMAT_ERROR] Single post', { status, mappedPost, socialId, credentialId });
                    throw new Error('Invalid media');
                }
                throw new Error(status.status);
            }
            await waitFor(3 * TimeInMilliseconds.SECOND);
        }
        return id;
    }

    async completePublish({ postId, creationId }: { postId: DbId; creationId: string }): Promise<IPostWithAttachments | void> {
        const post = await this._postsRepository.findOneOrFail({
            filter: { _id: postId },
            options: { populate: [{ path: 'attachments' }] },
        });

        assert(post.platformId, 'Missing platformId on post');
        const platform = await this._platformsRepository.getPlatformById(post.platformId.toString());
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                message: 'Platform not found in instagram complete publish use case',
                metadata: { platformId: post.platformId },
            });
        }

        try {
            const mappedPost = await this._publishMedia(creationId, post, platform);
            try {
                // THIS IS THE ONLY PLACE WHERE WE SET THE POST PUBLICATION STATUS TO "published"
                return this._postsRepository.upsert({
                    filter: { _id: post._id },
                    update: mappedPost,
                    options: {
                        populate: [{ path: 'attachments' }],
                    },
                });
            } catch (err) {
                if (String(err).match('duplicate key')) {
                    const alreadyPublishedPost = await this._postsRepository.findOneOrFail({
                        filter: {
                            socialId: mappedPost.socialId,
                            platformId: mappedPost.platformId,
                        },
                    });
                    if (alreadyPublishedPost.feedbackId) {
                        const postsWithFeedback = await this._postsRepository.find({ filter: { feedbackId: post.feedbackId } });
                        if (postsWithFeedback.length === 1) {
                            await this._feedbacksRepository.deleteOne({ filter: { _id: alreadyPublishedPost?.feedbackId } });
                        }
                    }
                    await this._postsRepository.deleteOne({ filter: { _id: alreadyPublishedPost._id } });
                    return this._postsRepository.upsert({
                        filter: { _id: post._id },
                        update: mappedPost,
                        options: {
                            lean: true,
                            populate: [{ path: 'attachments' }],
                        },
                    });
                }
            }
        } catch (e: any) {
            const errorMessage = e?.message;
            if (
                errorMessage?.match(/is not available/) ||
                errorMessage?.match(/n’est pas prêt/) ||
                errorMessage?.match(/unexpected error has occurred/) ||
                errorMessage?.match(/post took too long/)
            ) {
                // in case the error is just temporary reschedule
                if ((post.tries ?? 0) < 10) {
                    post.tries = (post.tries || 0) + 1;
                    logger.warn(`[COMPLETE_PUBLISH_IG][MEDIA_NOT_READY] Retrying to publish media tries number : ${post.tries}`, {
                        creationId,
                        postId: post._id,
                    });
                    await this._postsRepository.updateOne({ filter: { _id: post._id }, update: { tries: post.tries } });
                    setTimeout(async () => {
                        await sendCompletePublishPost({
                            creationId,
                            postId: post._id,
                        });
                    }, TimeInMilliseconds.SECOND * 5);
                } else {
                    logger.warn('[COMPLETE_PUBLISH_IG][MEDIA_NOT_READY] End of tries', e);
                    await this._sendAlertForPublishIgPost(
                        post,
                        e,
                        MalouErrorCode.MEDIA_NOT_READY_MAX_RETRIES_REACHED,
                        // eslint-disable-next-line max-len
                        `Max retries reached while trying to publish media on Instagram with error "does not exist" or "n'est pas prêt" or "unexpected error has occurred" for post ${post._id}`
                    );
                    throw new MalouError(MalouErrorCode.MEDIA_NOT_READY_MAX_RETRIES_REACHED, {
                        metadata: {
                            postId,
                            platform: PlatformKey.INSTAGRAM,
                        },
                    });
                }
            } else if (errorMessage?.match(/timeout/)) {
                logger.warn('[COMPLETE_PUBLISH_IG] Timeout error', e);
                await this._sendAlertForPublishIgPost(
                    post,
                    e,
                    MalouErrorCode.COMPLETE_PUBLISH_POST_TIMEOUT_ERROR,
                    `Timeout error while completing the publication of the post ${post._id} on Instagram`
                );
                throw new Error('ig_get_post_timeout_error');
            } else {
                logger.warn('[COMPLETE_PUBLISH_IG] Error', e);
                await this._sendAlertForPublishIgPost(
                    post,
                    e,
                    MalouErrorCode.COMPLETE_PUBLISH_POST_ERROR,
                    `Error while completing the publication of the post ${post._id} on Instagram`
                );
                throw e;
            }
        }
    }

    private async _publishMedia(creationId: string, _post: IPostWithAttachments, platform: Platform): Promise<Partial<IPost>> {
        const credentials = platform.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(platform.socialId, 'Missing socialId on platform');
        const { id: postId } = await facebookCredentialsUseCases.igPublishPost(credentialId, creationId, platform.socialId);

        logger.info('[IG_START_GET_POST]', { postId, platformId: platform._id });
        let igPost: IgPostData | undefined;
        const maxTries = 5;
        for (let i = 0; i < maxTries; i++) {
            try {
                igPost = await facebookCredentialsUseCases.igGetPost(credentialId, postId, platform.socialId);
                break;
            } catch (error: any) {
                if (isFbTimeoutError(error) && i < maxTries - 1) {
                    continue;
                }
                return {
                    socialId: postId,
                    published: PostPublicationStatus.PUBLISHED,
                    errorStage: 'complete publish ig post',
                    errorData: 'could_not_fetch_post',
                    isPublishing: false,
                }; // in case we cannot fetch the post we only set socialId
            }
        }
        assert(igPost, 'igPost is undefined');
        logger.info('[IG_END_GET_POST]', { postId, platformId: platform._id, igPostId: igPost.id });

        const postMapper = new InstagramPostMapper();
        return postMapper.mapToMalouPost({ post: igPost, platform }) as IPost;
    }

    async searchAccounts(text: string, platformId: string): Promise<any[]> {
        const platform = await this._platformsRepository.getPlatformById(platformId);
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND);
        }

        const credentials = platform.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        try {
            assert(platform.socialId, 'Missing socialId on platform');
            const result = await facebookCredentialsUseCases.igGetCompetitorInfo(credentialId, platform.socialId, text);
            return result;
        } catch (error: any) {
            if (isFacebookError(error)) {
                if (error.code === FacebookErrorCode.OAUTH) {
                    throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_ERROR);
                }
            }
            throw error;
        }
    }

    async oembed(url: string, platformId: string): Promise<any> {
        const platform = await this._platformsRepository.getPlatformById(platformId);
        const credentials = platform?.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }

        const result = await facebookCredentialsUseCases.igGetOembed(credentialId, url);
        return result;
    }

    async getCompetitorsPosts(credentialId: string, igPageId: string, accounts: any[]) {
        const promises = (accounts || []).map(async (account) => {
            const key = `igaccount.${igPageId}.${account.userName}`;
            const cached = await this._cache.get(key);
            if (cached !== null) {
                return cached;
            }
            try {
                const res = await facebookCredentialsUseCases.igGetCompetitorPosts(credentialId, igPageId, account.userName);
                for (const media of res.business_discovery.media.data) {
                    if (!media.media_url && !media.thumbnail_url) {
                        try {
                            const igEmbed = await facebookCredentialsUseCases.igGetOembed(credentialId, media.permalink);
                            media.media_url = igEmbed.thumbnail_url;
                        } catch (e) {
                            logger.info('[ERROR_OEMBED] - ', e);
                        }
                    }
                }
                res.business_discovery.media.data = res.business_discovery.media.data.map((post) =>
                    new InstagramPostMapper().mapToInsightPost(post)
                );
                void this._cache.set(key, res, TimeInSeconds.DAY);
                return res;
            } catch (e: any) {
                logger.info('[ERROR_IG_COMPETITORS] - ', e);
                if (e.message?.match(/FB_CREDENTIAL/)) {
                    throw e;
                }
                if (e.message?.match(/missing permissions/)) {
                    throw new Error('[FB_CREDENTIAL] Need more permissions');
                }
                return { error: true, errorData: e, username: account.userName };
            }
        });
        return Promise.all(promises);
    }

    async fetchPostsWithInsights(
        restaurantId: string,
        instagramFilters: PlatformInsightFiltersInstagram,
        shouldRaiseError?: boolean
    ): Promise<PostInsight[]> {
        const { startDate, endDate } = instagramFilters;
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.INSTAGRAM);
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: { restaurantId, key: PlatformKey.INSTAGRAM },
            });
        }
        const { socialId: pageId } = platform;
        const credentials = platform.credentials;
        const credentialId = credentials?.[0];

        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(pageId, 'Missing socialId on platform');
        const data = await facebookCredentialsUseCases.igGetPagePostsWithInsights(
            credentialId,
            pageId,
            startDate,
            endDate,
            shouldRaiseError
        );
        return data
            .filter((post) => !!post)
            .map((post) => new InstagramPostMapper().mapToInsightPost(post))
            .filter((post) => isBetween(post.createdAt, startDate, endDate));
    }

    async updatePost({ post }: { post: IPost }): Promise<IPost> {
        logger.info('[UPDATE_IG_POST] Can\t update Instagram post when already published');
        return post;
    }

    async deletePost({ post }: { post: IPost }): Promise<IPost> {
        logger.info('[UPDATE_IG_POST] Can\t delete Instagram post when already published');
        return post;
    }

    async synchronizeStories(platform: Platform): Promise<Partial<IPost>[]> {
        const credentials = platform.credentials;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(platform.socialId, 'Missing socialId on platform');
        const postMapper = new InstagramPostMapper();
        const igStories = await facebookCredentialsUseCases.igGetPageStories(credentialId, platform.socialId);
        if (!igStories) {
            return [];
        }
        return igStories.filter((story) => !!story.media_url).map((story) => postMapper.mapToMalouStory(story, platform));
    }

    async createStoryList(platform: IPlatform, posts: IPostWithAttachments[]): Promise<StoryToPublish[]> {
        const { credentials, socialId } = platform;
        const credentialId = credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(socialId, 'Missing socialId on platform');
        const storyList: { plannedPublicationDate: Date; postId: DbId; creationId: string; type: MediaType }[] = [];
        for (const post of posts) {
            assert(post.plannedPublicationDate, 'Post does not have a planned publication date');
            const creationId = await this._createStoryWithSeveralTries(post, credentialId.toString(), socialId);
            await this._assertIgMediaAvailable(credentialId.toString(), creationId, post._id);
            storyList.push({
                plannedPublicationDate: post.plannedPublicationDate,
                postId: post._id,
                creationId,
                type: post.attachments[0]?.type,
            });
        }
        return storyList;
    }

    private async _createStoryWithSeveralTries(post: IPostWithAttachments, credentialId: string, socialId: string): Promise<string> {
        const maxTries = 3;
        for (let i = 0; i < maxTries; i++) {
            try {
                const { id: creationId } = await facebookCredentialsUseCases.igCreateStory(post, credentialId, socialId);
                return creationId;
            } catch (error: any) {
                if (isFbTimeoutError(error) && i < maxTries - 1) {
                    await waitFor(5 * TimeInMilliseconds.SECOND);
                    continue;
                }
                logger.error('[COULD_NOT_CREATE_STORY_AFTER_RETRIES]', error);
                await this._sendAlertForPublishStory(post._id.toString(), error);
                throw new Error(error.malouErrorCode ?? error.message ?? JSON.stringify(error));
            }
        }
        throw new Error('Could not create story after retries');
    }

    async publishStory(platformId: string, creationId: string, _type: MediaType, postId: string): Promise<IPost> {
        try {
            const platform = await this._platformsRepository.getPlatformById(platformId);

            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    message: 'Platform not found in instagram posts use case',
                    metadata: { platformId },
                });
            }
            const { socialId } = platform;
            const credentials = platform.credentials;
            const credentialId = credentials?.[0];
            if (!credentialId) {
                throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
            }
            assert(socialId, 'Missing socialId on platform');
            const storySocialId = await this._publishStoryWithSeveralTries(credentialId, creationId, socialId);
            const story = await facebookCredentialsUseCases.igGetStory(credentialId, storySocialId);
            const postMapper = new InstagramPostMapper();
            return postMapper.mapToMalouStory(story, platform) as IPost;
        } catch (error: any) {
            logger.error('[PUBLISH_STORY_INSTAGRAM_ERROR]', error);
            await this._sendAlertForPublishStory(postId, error);
            throw error;
        }
    }

    private async _publishStoryWithSeveralTries(credentialId: string, creationId: string, socialId: string): Promise<string> {
        const maxTries = 3;
        for (let i = 0; i < maxTries; i++) {
            try {
                const { id: storySocialId } = await facebookCredentialsUseCases.igPublishPost(credentialId, creationId, socialId);
                return storySocialId;
            } catch (error: any) {
                if (isFbTimeoutError(error) && i < maxTries - 1) {
                    await waitFor(5 * TimeInMilliseconds.SECOND);
                    continue;
                }
                logger.error('[COULD_NOT_PUBLISH_STORY_AFTER_RETRIES]', error);
                throw new Error(error.message);
            }
        }
        throw new Error('Could not publish story after retries');
    }

    async upsertStoryAndSaveAttachments(
        restaurantId: ID,
        filter: FilterQuery<IPost>,
        update: Partial<IPost>
    ): Promise<IPostWithAttachments> {
        const currentStory = await this._postsRepository.findOne({ filter, options: { lean: true } });

        const mediaUrl = update.socialAttachments?.[0]?.urls?.original;
        const storyUpdate = { ...update };
        if (!currentStory?.attachments?.length && mediaUrl) {
            try {
                const uploadedMedia = await this._mediaUploaderService.uploadFromUrl(mediaUrl, restaurantId.toString());
                const mediaFormat = getFileFormatFromExtension(getFileExtension(mediaUrl));
                const id = newDbId().toString();
                const newMedia = await this._mediasRepository.createMedia(
                    new Media({
                        id,
                        socialId: id,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        ...uploadedMedia,
                        category: MediaCategory.ADDITIONAL,
                        format: mediaFormat,
                        type: Media.getMediaTypeFromFormat(mediaFormat),
                        restaurantId: restaurantId.toString(),
                        folderId: null,
                    })
                );
                storyUpdate.attachments = [toDbId(newMedia.id)];
            } catch (e) {
                logger.warn('[ERROR_UPSERT_STORY_AND_SAVE_ATTACHMENTS_INSTAGRAM]', e);
            }
        }
        return this._postsRepository.upsert({
            filter,
            update: storyUpdate,
            options: {
                populate: [{ path: 'attachments' }],
            },
        });
    }

    private async _assertIgMediaAvailable(credentialId: string, creationId: string, postId: ID): Promise<void> {
        const MAX_RETRIES = 20 * 6;
        let containerStatus = await facebookCredentialsUseCases.igCheckCreationStatus(credentialId, creationId);
        for (let index = 0; index < MAX_RETRIES; index++) {
            if (containerStatus.status_code === IgMediaContainerStatus.FINISHED) {
                break;
            }
            containerStatus = await facebookCredentialsUseCases.igCheckCreationStatus(credentialId, creationId);
            await waitFor(10000);
        }

        if (containerStatus.status_code !== 'FINISHED') {
            throw new Error(`${containerStatus.status_code} - ${containerStatus.status} for creationId ${creationId} on post ${postId}`);
        }
    }

    private _isInvalidMedia = (status: { id: string; status: string; status_code: IgMediaContainerStatus }) =>
        status?.status?.match(new RegExp(INSTAGRAM_NOT_SUPPORTED_FORMAT_ERROR.toString()));

    private async _getRestaurantFromPost(post: { restaurantId: DbId }): Promise<IRestaurant | null> {
        const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: post?.restaurantId } });

        return restaurant;
    }

    private async _sendAlertForPublishIgPost(
        post: { _id: DbId; restaurantId: DbId },
        e: Error,
        malouErrorCode: MalouErrorCode,
        description: string
    ): Promise<void> {
        const restaurantFromPost = await this._getRestaurantFromPost(post);
        const errorMessage = e.message ?? JSON.stringify(e);

        this._slackService.sendAlert({
            channel: SlackChannel.POSTS_V1_ALERTS,
            data: {
                err: new MalouError(malouErrorCode, { metadata: { rawError: errorMessage }, message: errorMessage }),
                endpoint: `restaurants/${restaurantFromPost?._id}/social/socialposts?postId=${post._id}`,
                metadata: { description, restaurantName: restaurantFromPost?.name ?? '' },
            },
        });
    }

    private async _sendAlertForPublishStory(postId: string, error: Error | MalouError): Promise<void> {
        const errorMessage = error.message ?? JSON.stringify(error);
        const malouErrorCode = this._isMalouError(error) ? error.malouErrorCode : MalouErrorCode.COMPLETE_PUBLISH_STORY_ERROR;

        const post = await this._postsRepository.findById(postId);
        assert(post, 'Post not found');
        const restaurantFromPost = await this._getRestaurantFromPost(post);
        await this._createPostErrorNotificationProducer.execute({ postId: post._id.toString() });

        this._slackService.sendAlert({
            channel: SlackChannel.POSTS_V1_ALERTS,
            data: {
                err: new MalouError(malouErrorCode, {
                    metadata: { rawError: errorMessage, platformKey: PlatformKey.INSTAGRAM },
                    message: errorMessage,
                }),
                endpoint: `restaurants/${restaurantFromPost?._id}/social/socialposts?postId=${post._id}`,
                metadata: { description: 'Error while publishing story on Instagram', restaurantName: restaurantFromPost?.name ?? '' },
            },
        });
    }

    private async _sendExpiredLocationEmail(postId: DbId): Promise<void> {
        const post = await this._postsRepository.findOneOrFail({ filter: { _id: postId } });

        await this._mailingUseCases.sendEmail(EmailCategory.POST_NOTIFICATION, EmailType.POST_LOCATION_EXPIRED, {
            userId: post.author?._id.toString(),
            post,
            restaurantId: post.restaurantId.toString(),
        });
    }

    private async _handlePublishError(postId: DbId, error: any): Promise<void> {
        logger.warn('[PUBLISH_POST][IG] - Failed publication with error - ', {
            postId,
            error,
        });

        const errorData =
            error.malouErrorCode === MalouErrorCode.MEDIA_NOT_READY_MAX_RETRIES_REACHED
                ? 'Publishing the post took too long'
                : (error.malouErrorCode ?? `message::${error?.message}--fbCode::${error?.code}--fbSubcode::${error?.error_subcode}`);

        const post = await this._postsRepository.upsert({
            filter: { _id: postId },
            update: {
                published: PostPublicationStatus.ERROR,
                errorData,
                errorStage: 'publish ig post',
                isPublishing: false,
            },
            options: { populate: [{ path: 'attachments' }], lean: true, new: true },
        });

        await this._sendAlertForPublishIgPost(
            post,
            new Error(errorData),
            MalouErrorCode.PUBLISH_POST_ERROR,
            'An error occurred while publishing the post on Instagram'
        );

        await this._sendPublicationFailedEmail(postId);
    }

    /* TODO this is a duplicate function from PostsUseCases to avoid cyclic dependency.
     * In release v1.4: move this function from PostsUseCases to another class in order to be able to call it
     * from any posts use case.
     */
    private async _sendPublicationFailedEmail(postId: DbId): Promise<void> {
        const post = await this._postsRepository.findOneOrFail({
            filter: { _id: postId },
            options: { lean: true, populate: [{ path: 'feedback' }, { path: 'attachments' }] },
        });
        if (post.author?._id) {
            await this._createPostErrorNotificationProducer.execute({ postId: post._id.toString() });
        } else {
            logger.error('[SEND_PUBLICATION_FAILED_EMAIL] - No author found for post', { postId });
        }
    }

    private _isMalouError(error: Error | MalouError): error is MalouError {
        return error instanceof MalouError && !!error.malouErrorCode;
    }
}
