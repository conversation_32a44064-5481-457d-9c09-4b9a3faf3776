import { sortBy } from 'lodash';
import { singleton } from 'tsyringe';

import { GetTop3PostsInsightsBodyDto, GetTop3PostsInsightsResponseDto } from '@malou-io/package-dto';
import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import { PlatformInsightFiltersApiFactory } from ':helpers/filters/platform-insight-filters-api-factory';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FbPostInsight } from ':modules/posts/platforms/facebook/facebook-post.interface';
import { FacebookPostsUseCases } from ':modules/posts/platforms/facebook/use-cases';
import { PostInsight } from ':modules/posts/platforms/instagram/instagram-post.interface';
import { InstagramPostsUseCases } from ':modules/posts/platforms/instagram/use-cases';
import { TiktokPostsUseCases } from ':modules/posts/platforms/tiktok/tiktok-post.use-cases';
import { PlatformFollowersInsightForTop3Posts, PlatformWithRestaurantDetails } from ':modules/posts/posts.interface';

@singleton()
export class GetTop3PostsInsightsUseCase {
    constructor(
        private readonly _platformRepository: PlatformsRepository,
        private readonly _platformInsightsRepository: PlatformInsightsRepository,
        private readonly _instagramPostsUseCases: InstagramPostsUseCases,
        private readonly _facebookPostsUseCases: FacebookPostsUseCases,
        private readonly _tiktokPostsUseCases: TiktokPostsUseCases
    ) {}

    async execute(body: GetTop3PostsInsightsBodyDto): Promise<GetTop3PostsInsightsResponseDto[]> {
        const { restaurantIds, platformKeys } = body;
        const startDate = new Date(body.startDate);
        const endDate = new Date(body.endDate);

        const platformsWithRestaurants: PlatformWithRestaurantDetails[] =
            await this._platformRepository.getPlatformsWithRestaurantDetailsByRestaurantIdsAndPlatformKeys(restaurantIds, platformKeys);

        const socialIdsFilter = platformsWithRestaurants.map((platform) => platform.socialId).filter(isNotNil);

        const platformInsights: PlatformFollowersInsightForTop3Posts[] =
            await this._platformInsightsRepository.getFollowerMetricByPlatformsAndDatesV2(
                platformKeys,
                socialIdsFilter,
                startDate,
                endDate
            );

        const top3postInsightsPerPlatformKeyPromises: Promise<GetTop3PostsInsightsResponseDto[]>[] = platformKeys.map((platformKey) => {
            switch (platformKey) {
                case PlatformKey.INSTAGRAM:
                    return this._getInstagramTop3PostsInsights(platformsWithRestaurants, platformInsights, startDate, endDate);
                case PlatformKey.FACEBOOK:
                    return this._getFacebookTop3PostsInsights(platformsWithRestaurants, platformInsights, startDate, endDate);
                case PlatformKey.TIKTOK:
                    return this._getTiktokTop3PostsInsights(platformsWithRestaurants, platformInsights, startDate, endDate);
                default:
                    return Promise.resolve([]);
            }
        });

        const mergedArray = (await Promise.all(top3postInsightsPerPlatformKeyPromises)).flat();

        const mergedArrayFiltered = mergedArray.filter((e) => isNotNil(e.engagementRate));

        return sortBy(mergedArrayFiltered, (p) => p.engagementRate)
            .reverse()
            .slice(0, 3);
    }

    private async _getInstagramTop3PostsInsights(
        platformsWithRestaurants: PlatformWithRestaurantDetails[],
        platformInsights: PlatformFollowersInsightForTop3Posts[],
        startDate: Date,
        endDate: Date
    ): Promise<GetTop3PostsInsightsResponseDto[]> {
        const concernedPlatforms = platformsWithRestaurants.filter((platform) => platform.key === PlatformKey.INSTAGRAM);
        const concernedPlatformInsights = platformInsights.filter((platform) => platform.platformKey === PlatformKey.INSTAGRAM);
        const instagramFilters = PlatformInsightFiltersApiFactory.createPlatformInsightFiltersApi(PlatformKey.INSTAGRAM, {
            startDate,
            endDate,
        });

        const instagramPromises: Promise<{
            platformWithRestaurant: PlatformWithRestaurantDetails;
            postInsights: PostInsight[];
        }>[] = concernedPlatforms.map(async (platformWithRestaurant) => {
            const postInsights = await this._instagramPostsUseCases
                .fetchPostsWithInsights(platformWithRestaurant.restaurantId, instagramFilters)
                .catch((_) => []);
            return {
                platformWithRestaurant,
                postInsights,
            };
        });

        const instagramPostsInsights = (await Promise.all(instagramPromises)).flat();

        return instagramPostsInsights
            .map((value) =>
                value.postInsights.map((postInsight) =>
                    this._mapInstagramPostInsightToResponseDto(value.platformWithRestaurant, postInsight, concernedPlatformInsights)
                )
            )
            .flat();
    }

    private async _getFacebookTop3PostsInsights(
        platformsWithRestaurants: PlatformWithRestaurantDetails[],
        platformInsights: PlatformFollowersInsightForTop3Posts[],
        startDate: Date,
        endDate: Date
    ): Promise<GetTop3PostsInsightsResponseDto[]> {
        const concernedPlatforms = platformsWithRestaurants.filter((platform) => platform.key === PlatformKey.FACEBOOK);
        const concernedPlatformInsights = platformInsights.filter((platform) => platform.platformKey === PlatformKey.FACEBOOK);
        const facebookFilters = PlatformInsightFiltersApiFactory.createPlatformInsightFiltersApi(PlatformKey.FACEBOOK, {
            startDate,
            endDate,
        });

        const facebookPromises: Promise<{
            platformWithRestaurant: PlatformWithRestaurantDetails;
            postsInsights: FbPostInsight[];
        }>[] = concernedPlatforms.map(async (platformWithRestaurant) => {
            const postsInsights = await this._facebookPostsUseCases
                .fetchPostsWithInsightsV2(platformWithRestaurant.restaurantId, facebookFilters)
                .catch((_) => []);
            return {
                platformWithRestaurant,
                postsInsights: postsInsights,
            };
        });

        const facebookPostsInsights = (await Promise.all(facebookPromises)).flat();

        return facebookPostsInsights
            .map((value) =>
                value.postsInsights.map((postInsight) =>
                    this._mapFacebookPostInsightToResponseDto(value.platformWithRestaurant, postInsight, concernedPlatformInsights)
                )
            )
            .flat();
    }

    private async _getTiktokTop3PostsInsights(
        platformsWithRestaurants: PlatformWithRestaurantDetails[],
        platformInsights: PlatformFollowersInsightForTop3Posts[],
        startDate: Date,
        endDate: Date
    ): Promise<GetTop3PostsInsightsResponseDto[]> {
        const concernedPlatforms = platformsWithRestaurants.filter((platform) => platform.key === PlatformKey.TIKTOK);
        const concernedPlatformInsights = platformInsights.filter((platform) => platform.platformKey === PlatformKey.TIKTOK);
        const tiktokFilters = PlatformInsightFiltersApiFactory.createPlatformInsightFiltersApi(PlatformKey.TIKTOK, {
            startDate,
            endDate,
        });

        const tiktokPromises: Promise<{
            platformWithRestaurant: PlatformWithRestaurantDetails;
            postInsights: PostInsight[];
        }>[] = concernedPlatforms.map(async (platformWithRestaurant) => {
            const postInsights = await this._tiktokPostsUseCases
                .fetchPostsWithInsights(platformWithRestaurant.restaurantId, tiktokFilters)
                .catch((_) => []);
            return {
                platformWithRestaurant,
                postInsights,
            };
        });

        const tiktokPostsInsights = (await Promise.all(tiktokPromises)).flat();

        return tiktokPostsInsights
            .map((value) =>
                value.postInsights.map((postInsight) =>
                    this._mapTiktokPostInsightToResponseDto(value.platformWithRestaurant, postInsight, concernedPlatformInsights)
                )
            )
            .flat();
    }

    private _mapInstagramPostInsightToResponseDto(
        platform: PlatformWithRestaurantDetails,
        postInsight: PostInsight,
        platformInsights: PlatformFollowersInsightForTop3Posts[]
    ): GetTop3PostsInsightsResponseDto {
        const engagement = (postInsight.likes ?? 0) + (postInsight.comments ?? 0) + (postInsight.shares ?? 0) + (postInsight.saved ?? 0);
        const followers = this._getFollowersCountNearDate(platform, platformInsights, postInsight.createdAt);
        const engagementRate = followers ? (engagement / followers) * 100 : undefined;
        return {
            restaurantName: platform.restaurant?.internalName ?? platform.restaurant?.name ?? '',
            restaurantAddress: platform.restaurant?.address?.formattedAddress,
            platformKey: PlatformKey.INSTAGRAM,
            createdAt: postInsight.createdAt,
            postType: postInsight.postType,
            url: postInsight.url,
            thumbnailUrl: postInsight.thumbnail,
            likes: postInsight.likes,
            comments: postInsight.comments,
            shares: postInsight.shares,
            saves: postInsight.saved,
            impressions: postInsight.impressions ?? postInsight.plays,
            engagementRate: engagementRate,
        };
    }

    private _mapFacebookPostInsightToResponseDto(
        platform: PlatformWithRestaurantDetails,
        postInsight: FbPostInsight,
        platformInsights: PlatformFollowersInsightForTop3Posts[]
    ): GetTop3PostsInsightsResponseDto {
        const engagement = (postInsight.likes ?? 0) + (postInsight.comments ?? 0) + (postInsight.shares ?? 0);
        const followers = this._getFollowersCountNearDate(platform, platformInsights, postInsight.createdAt);
        const engagementRate = followers ? (engagement / followers) * 100 : undefined;
        return {
            restaurantName: platform.restaurant?.internalName ?? platform.restaurant?.name ?? '',
            restaurantAddress: platform.restaurant?.address?.formattedAddress,
            platformKey: PlatformKey.FACEBOOK,
            createdAt: postInsight.createdAt,
            postType: postInsight.postType,
            url: postInsight.url,
            likes: postInsight.likes,
            comments: postInsight.comments,
            shares: postInsight.shares,
            impressions: postInsight.impressions,
            engagementRate: engagementRate,
        };
    }

    private _mapTiktokPostInsightToResponseDto(
        platform: PlatformWithRestaurantDetails,
        postInsight: PostInsight,
        platformInsights: PlatformFollowersInsightForTop3Posts[]
    ): GetTop3PostsInsightsResponseDto {
        const engagement = (postInsight.likes ?? 0) + (postInsight.comments ?? 0) + (postInsight.shares ?? 0) + (postInsight.saved ?? 0);
        const followers = this._getFollowersCountNearDate(platform, platformInsights, postInsight.createdAt);
        const engagementRate = followers ? (engagement / followers) * 100 : undefined;
        return {
            restaurantName: platform.restaurant?.internalName ?? platform.restaurant?.name ?? '',
            restaurantAddress: platform.restaurant?.address?.formattedAddress,
            platformKey: PlatformKey.TIKTOK,
            createdAt: postInsight.createdAt,
            postType: postInsight.postType,
            url: postInsight.url,
            thumbnailUrl: postInsight.thumbnail,
            likes: postInsight.likes,
            comments: postInsight.comments,
            shares: postInsight.shares,
            saves: postInsight.saved,
            impressions: postInsight.impressions ?? postInsight.plays,
            engagementRate: engagementRate,
        };
    }

    private _getFollowersCountNearDate(
        platform: PlatformWithRestaurantDetails,
        platformInsights: PlatformFollowersInsightForTop3Posts[],
        date: Date
    ): number | undefined {
        const platformInsightsFiltered = platformInsights.filter(
            (platformInsight) => platformInsight.platformKey === platform.key && platformInsight.socialId === platform.socialId
        );
        const followersByDatesWithDateDifference = platformInsightsFiltered.map((platformInsight) => ({
            value: platformInsight.value,
            dateDifference: Math.abs(date.getTime() - platformInsight.date.getTime()),
        }));
        return sortBy(followersByDatesWithDateDifference, (e) => e.dateDifference)[0]?.value ?? undefined;
    }
}
