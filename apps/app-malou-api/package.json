{"apidoc": {"apidoc": {"title": "Custom apiDoc browser title", "url": "https://api.github.com/v1"}, "description": "apiDoc for malou API", "name": "@malou-io/app-api", "version": "0.1.0"}, "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>"}, "dependencies": {"@casl/ability": "^5.4.3", "@faker-js/faker": "^6.3.1", "@google-cloud/pubsub": "^2.19.0", "@googleapis/mybusinessbusinessinformation": "^4.0.9", "@googleapis/mybusinessplaceactions": "^1.0.7", "@googleapis/mybusinessverifications": "^2.0.2", "@growthbook/growthbook": "^0.36.0", "@hapi/joi": "^17.1.1", "@malou-io/package-crawlers": "workspace:*", "@malou-io/package-dto": "workspace:*", "@malou-io/package-emails": "workspace:*", "@malou-io/package-models": "workspace:*", "@malou-io/package-utils": "workspace:*", "@octokit/auth-app": "^6.1.3", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-metrics-otlp-proto": "^0.52.1", "@opentelemetry/resources": "1.25.1", "@opentelemetry/sdk-metrics": "1.25.1", "@opentelemetry/sdk-node": "^0.52.1", "@opentelemetry/semantic-conventions": "^1.25.0", "@react-email/render": "^0.0.12", "@sentry/node": "^7.113.0", "@sentry/profiling-node": "^7.113.0", "@slack/bolt": "^4.2.1", "agenda": "4.4.0", "agendash": "^4.0.0", "aws-sdk": "^2.1192.0", "axios": "^1.9.0", "bcrypt-nodejs": "0.0.3", "body-parser": "^1.19.0", "busboy": "^1.6.0", "chalk": "^4.1.2", "cheerio": "^1.0.0-rc.12", "cloudinary": "^2.4.0", "connect-redis": "^6.0.0", "convert-excel-to-json": "^1.7.0", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "country-locale-map": "^1.9.9", "crypto-js": "^4.0.0", "csv-parse": "^4.16.3", "csv-writer": "^1.6.0", "dot-object": "^2.1.3", "dotenv": "^5.0.1", "emoji-regex": "^10.3.0", "express": "^4.17.1", "express-basic-auth": "^1.2.0", "express-session": "^1.17.1", "fast-levenshtein": "^3.0.0", "fb": "^2.0.0", "ffprobe": "^1.1.2", "file-type": "^16.5.3", "firebase-admin": "^11.9.0", "flat": "^5.0.2", "form-data": "^4.0.0", "formidable": "^1.2.6", "gaxios": "^6.7.1", "google-auth-library": "^9.14.1", "google-libphonenumber": "^3.2.10", "google-spreadsheet": "^3.0.11", "handlebars": "^4.7.7", "handlebars-helpers": "^0.10.0", "helmet": "^7.1.0", "hoek": "^5.0.4", "http": "0.0.1-security", "http-proxy-middleware": "^2.0.6", "image-size": "^1.0.0", "install": "^0.13.0", "ioredis": "^5.2.4", "joi": "^17.6.0", "json2csv": "^5.0.1", "jsonwebtoken": "^9.0.0", "languagedetect": "^2.0.0", "libphonenumber-js": "^1.7.52", "lodash": "^4.17.21", "luxon": "^1.28.1", "mailgun-js": "^0.22.0", "moment": "^2.29.2", "mongoose": "^8.14.1", "mongoose-unique-validator": "^3.0.0", "multer": "1.4.5-lts.1", "neverthrow": "^8.1.1", "node-fetch": "^2.6.6", "node-html-parser": "^7.0.1", "nodemailer": "^6.9.2", "object-hash": "^3.0.0", "openai": "^3.2.1", "p-limit": "^6.2.0", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "prompts": "^2.4.2", "pug": "^3.0.1", "pusher": "^5.0.1", "qs": "^6.12.0", "readline-sync": "^1.4.10", "reflect-metadata": "^0.1.13", "request-promise-native": "^1.0.8", "rimraf": "^3.0.2", "semver": "^5.7.1", "sharp": "=0.33.1", "shelljs": "^0.8.5", "sinon": "^7.5.0", "sqs-consumer": "^5.5.0", "sqs-producer": "^2.1.0", "ts-migrate-mongoose": "^3.7.0", "tslib": "^1.11.2", "tsyringe": "^4.7.0", "type-fest": "^4.26.1", "url": "^0.11.0", "user-agents": "^1.1.498", "utility-types": "^3.11.0", "uuid": "^8.3.2", "winston": "^3.8.2", "xlsx": "^0.18.5", "yargs": "^17.5.1", "zod": "^3.21.4"}, "devDependencies": {"@babel/core": "^7.17.12", "@babel/plugin-proposal-decorators": "^7.20.2", "@babel/preset-env": "^7.17.12", "@babel/preset-typescript": "^7.17.12", "@malou-io/package-config": "workspace:*", "@sentry/cli": "^2.32.1", "@sentry/types": "^7.113.0", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/bcrypt-nodejs": "0.0.30", "@types/body-parser": "^1.19.2", "@types/busboy": "^1.5.4", "@types/chai": "^4.3.1", "@types/cli-progress": "^3.11.6", "@types/connect-redis": "^0.0.18", "@types/cookie-parser": "^1.4.3", "@types/cors": "^2.8.12", "@types/crypto-js": "^4.1.1", "@types/dot-object": "^2.1.2", "@types/dotenv": "^4.0.3", "@types/eslint": "^8.48.0", "@types/express": "^4.17.6", "@types/express-session": "^1.15.15", "@types/ffprobe": "^1.1.8", "@types/flat": "^5.0.2", "@types/frisby": "^2.0.14", "@types/google-libphonenumber": "^7.4.23", "@types/google-spreadsheet": "^3.2.3", "@types/hapi__joi": "^17.1.8", "@types/hoek": "^4.1.4", "@types/jest": "^29.0.0", "@types/js-levenshtein": "^1.1.1", "@types/json2csv": "^5.0.3", "@types/jsonwebtoken": "^7.2.8", "@types/lodash": "^4.14.182", "@types/luxon": "^1.27.1", "@types/mailgun-js": "^0.22.12", "@types/mongodb": "^3.6.9", "@types/mongoose": "^5.11.97", "@types/mongoose-unique-validator": "^1.0.6", "@types/multer": "^1.4.7", "@types/multer-s3": "^2.7.12", "@types/node": "^16.3.1", "@types/node-fetch": "^3.0.3", "@types/nodemailer": "^6.4.8", "@types/npm": "^7.19.0", "@types/object-hash": "^3.0.6", "@types/passport": "^0.4.7", "@types/passport-jwt": "^3.0.2", "@types/probe-image-size": "^7.2.5", "@types/prompts": "^2.4.2", "@types/pug": "^2.0.6", "@types/qs": "^6.9.14", "@types/request-promise-native": "^1.0.18", "@types/rimraf": "^3.0.2", "@types/semver": "^7.3.9", "@types/sharp": "0.30.5", "@types/shelljs": "^0.8.11", "@types/sinon": "^7.5.2", "@types/supertest": "^2.0.12", "@types/uuid": "^8.3.4", "@types/yargs": "^17.0.10", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "babel-jest": "^28.1.0", "babel-plugin-transform-typescript-metadata": "^0.3.2", "builder-pattern": "^2.2.0", "chai": "^4.2.0", "cli-progress": "^3.12.0", "concurrently": "^8.2.2", "eslint": "^8.48.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "frisby": "^2.1.3", "jest": "^29.0.0", "jest-extended": "^4.0.2", "jimp": "^0.22.10", "mongodb": "6.16.0", "node-addon-api": "^8.2.2", "node-gyp": "^10.2.0", "nodemon": "^2.0.16", "prettier": "^3.5.3", "probe-image-size": "^7.2.3", "qrcode": "^1.5.3", "supertest": "^6.1.6", "ts-node": "^10.8.1", "typesafe-i18n": "^5.26.2", "typescript": "^5.3.3", "utility-types": "^3.10.0"}, "license": "MIT", "name": "@malou-io/app-api", "private": true, "repository": {"url": "https://github.com/ypicard/malou.git"}, "scripts": {"apidoc": "apidoc -i ./ -e 'node_modules' -o apidoc/", "build": "NODE_OPTIONS=--max-old-space-size=8192 tsc && tsc-alias && pnpm run build-after", "build-after": "cp -r assets dist/", "build-clean": "rm -rf ./dist && rm -rf .turbo && rm -f tsconfig.tsbuildinfo", "build-development": "NODE_OPTIONS=--max-old-space-size=8192 tsc && tsc-alias", "build-incremental": "NODE_OPTIONS=--max-old-space-size=8192 tsc -b src --incremental", "build-production": "NODE_OPTIONS=--max-old-space-size=8192 tsc && tsc-alias", "build-staging": "NODE_OPTIONS=--max-old-space-size=8192 tsc && tsc-alias", "create-api-key": "NODE_ENV=local ts-node -T src/tasks/api-keys/create-api-key.js", "create-user": "NODE_ENV=local ts-node -T src/tasks/users/create-user.js", "db:seed": "docker compose exec -T mongodb sh -c \"mongosh --eval 'use malou' --eval 'db.dropDatabase()' && mongodump --uri $MONGODB_URI -o ./mongo-backup && mongorestore --uri 'mongodb://127.0.0.1:27017/malou' ./mongo-backup/$NODE_ENV\"", "delete-restaurant": "ts-node -T src/tasks/restaurants/delete-restaurant.ts", "fixtures": "ts-node -T fixtures/index.ts", "format": "prettier --write \"**/*.{ts,js,md}\"", "format:check": "prettier \"**/*.{ts,js,md}\" --check", "i18n": "typesafe-i18n", "lint-old": "eslint \"**/*.ts\" --config .eslintrc.js --ignore-pattern 'src/providers/*' --ignore-pattern 'src/microservices/*'", "lint-new": "eslint \"src/microservices/**/*.ts\" \"src/providers/**/*.ts\" --config .eslintrc.v2.js", "lint": "pnpm run lint-old && pnpm run lint-new", "lint-fix-old": "eslint \"**/*.ts\" --fix --config .eslintrc.js --ignore-pattern 'src/providers/*' --ignore-pattern 'src/microservices/*'", "lint-fix-new": "eslint \"src/microservices/**/*.ts\" \"src/providers/**/*.ts\" --fix --config .eslintrc.v2.js", "lint-fix": "pnpm run lint-fix-old && pnpm run lint-fix-new", "lint-staged": "lint-staged --no-stash", "nodemon-api": "nodemon -T ./src/server.ts", "nodemon-worker": "nodemon -T ./src/agenda-jobs/worker.ts", "preinstall": "npx only-allow pnpm", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./dist && sentry-cli sourcemaps upload --org ${SENTRY_ORG} --project ${SENTRY_PROJECT} --release ${SENTRY_RELEASE} ./dist", "start": "node dist/src/server", "start-dev": "ts-node -T ./node-version-checker.ts && concurrently --names \"API,WORKER\" -c \"bgMagenta.bold,bgBlue.bold\" \"NODE_ENV=development pnpm run watch-api\" \"NODE_ENV=development pnpm run watch-worker\"", "start-local": "docker compose up -d --remove-orphans && ts-node -T ./node-version-checker.ts && concurrently --names \"API,WORKER\" -c \"bgMagenta.bold,bgBlue.bold\" \"NODE_ENV=local pnpm run watch-api\" \"NODE_ENV=local pnpm run watch-worker\"", "start-monitoring": "docker compose -f docker-compose.monitoring.yml up", "start-production": "ts-node -T ./node-version-checker.ts && concurrently --names \"API,WORKER\" -c \"bgMagenta.bold,bgBlue.bold\" \"NODE_ENV=production pnpm run watch-api\" \"NODE_ENV=production pnpm run watch-worker\"", "start-staging": "ts-node -T ./node-version-checker.ts && concurrently --names \"API,WORKER\" -c \"bgMagenta.bold,bgBlue.bold\" \"NODE_ENV=staging pnpm run watch-api\" \"NODE_ENV=staging pnpm run watch-worker\"", "start-workers": "node --max_old_space_size=8192 dist/src/agenda-jobs/worker.js", "test": "jest", "test:cov": "jest --coverage", "test:integration": "NODE_ENV=local-tests jest int -c jest.config.integration.ts --force-exit --runInBand", "test:integration:debug": "NODE_ENV=local-tests jest int -c jest.config.integration.ts --watch --runInBand", "test:integration:single": "NODE_ENV=local-tests jest -c jest.config.integration.ts --watch --runInBand", "test:unit": "NODE_OPTIONS=--max-old-space-size=8192 NODE_ENV=local-tests jest unit -c jest.config.unit.ts --force-exit --color", "test:unit:debug": "NODE_ENV=local-tests jest unit -c jest.config.unit.ts --watch --color", "test:unit:single": "NODE_ENV=local-tests jest -c jest.config.unit.ts", "test:watch": "jest --watchAll", "tsc": "tsc src", "watch": "tsc src -w", "watch-api": "concurrently \"pnpm run nodemon-api\" \"pnpm run i18n\"", "watch-worker": "pnpm run nodemon-worker"}, "version": "0.0.0"}