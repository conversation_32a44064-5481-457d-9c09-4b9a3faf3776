# Alpine image
FROM node:22.15.0-alpine3.21 AS alpine
RUN apk add --no-cache libc6-compat build-base cmake py3-pip pkgconf \
    # dependencies to build sharp: \
    vips-dev=8.15.3-r5 libheif-dev=1.19.5-r0 \
    # we use ffprobe in the backend
    ffmpeg=6.1.2-r1

# Setup pnpm and turbo on the alpine base
FROM alpine AS base
RUN npm install pnpm@10.6.5 turbo@1.13.4 --global \
    && pnpm config set store-dir ~/.pnpm-store

# Prune projects
FROM base AS pruner

WORKDIR /app
COPY . .

# Writes package-lock.json and related fields to /app/out/json
# See https://turbo.build/repo/docs/guides/tools/docker
RUN turbo prune --scope=@malou-io/app-api --docker

COPY tsconfig.json tsconfig.options.json scripts/sentry-upload-api-sourcemaps.sh /app/out/full/
COPY scripts/test-sharp-libvips /app/out/full/scripts/test-sharp-libvips

# Build the project
FROM base AS builder

ARG GIT_PKG_ACCESS_TOKEN
ARG ENV
ARG SENTRY_AUTH_TOKEN
ARG GIT_COMMIT_SHA

WORKDIR /app

# Copy lockfile and package.json's of isolated subworkspace
COPY --from=pruner /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=pruner /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=pruner /app/out/json/ .

# First install the dependencies (as they change less often)
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm install --prod=false --frozen-lockfile

# Copy source code of isolated subworkspace
COPY --from=pruner /app/out/full/ .

RUN pnpm exec turbo build --filter=@malou-io/app-api

RUN chmod +x ./sentry-upload-api-sourcemaps.sh && ./sentry-upload-api-sourcemaps.sh
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm prune --prod --no-optional
RUN rm -rf ./**/*/src

# Final base image for API
FROM alpine AS api

ARG ENV
ARG GIT_COMMIT_SHA
ARG BRANCH_NAME

# Don't run container as root
RUN addgroup --system --gid 1001 nodejs \
 && adduser --system --uid 1001 nodejs
USER nodejs

WORKDIR /app
COPY --from=builder --chown=nodejs:nodejs /app .
WORKDIR /app/apps/app-malou-api

ENV NODE_ENV=${ENV}
ENV GIT_COMMIT_SHA=${GIT_COMMIT_SHA}
ENV BRANCH_NAME=${BRANCH_NAME}

# Ideally we should check the Sharp and libvips installation on the CI, but we
# don’t use this Dockerfile in CI checks that are triggered for pull requests.
# So instead we do this here in order to make sure that we can decode HEIC pictures:
RUN node /app/scripts/test-sharp-libvips/test.js

# We define commands for the worker and API in the task definition files to avoid rebuilding the image
# Prefer using node than pnpm script
# https://medium.com/@nodepractices/docker-best-practices-with-node-js-e044b78d8f67
