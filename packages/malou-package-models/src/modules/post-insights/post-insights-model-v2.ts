import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { postInsightJSONSchemaV2 } from ':modules/post-insights/post-insights-schema-v2';

const postInsightSchemaV2 = createMongooseSchemaFromJSONSchema(postInsightJSONSchemaV2);

postInsightSchemaV2.virtual('post', {
    ref: 'Post',
    localField: 'socialId',
    foreignField: 'socialId',
    justOne: true,
});

postInsightSchemaV2.index({ platformKey: 1, socialId: 1 }, { unique: true });

export type IPostInsightV2 = FromSchema<
    typeof postInsightJSONSchemaV2,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const PostInsightModelV2 = mongoose.model<IPostInsightV2>(postInsightJSONSchemaV2.title, postInsightSchemaV2);
