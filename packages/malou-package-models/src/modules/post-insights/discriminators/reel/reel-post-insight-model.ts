import { FromSchema } from 'json-schema-to-ts';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { reelPostInsightJSONSchema } from ':modules/post-insights/discriminators/reel/reel-post-insight-schema';
import { IPostInsightV2, PostInsightModelV2 } from ':modules/post-insights/post-insights-model-v2';

const reelPostInsightSchema = createMongooseSchemaFromJSONSchema(reelPostInsightJSONSchema);

export type IReelPostInsight = IPostInsightV2 &
    FromSchema<
        typeof reelPostInsightJSONSchema,
        {
            keepDefaultedPropertiesOptional: true;
            deserialize: DESERIALIZE_OPTIONS;
        }
    >;

export const ReelPostInsightModel = PostInsightModelV2.discriminator<IReelPostInsight>(
    reelPostInsightJSONSchema.title,
    reelPostInsightSchema
);
