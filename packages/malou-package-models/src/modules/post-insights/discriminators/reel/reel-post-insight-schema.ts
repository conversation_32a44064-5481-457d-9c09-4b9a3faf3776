import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const reelPostInsightJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'ReelPostInsight',
    type: 'object',
    discriminator: true,
    additionalProperties: false,
    properties: {
        data: {
            type: 'object',
            additionalProperties: false,
            properties: {
                impressions: {
                    type: 'number',
                    default: 0,
                },
                reach: {
                    type: 'number',
                    nullable: true,
                    default: null,
                },
                plays: {
                    type: 'number',
                    default: 0,
                },
                likes: {
                    type: 'number',
                    default: 0,
                },
                comments: {
                    type: 'number',
                    default: 0,
                },
                shares: {
                    type: 'number',
                    default: 0,
                },
                saved: {
                    type: 'number',
                    nullable: true,
                    default: null,
                },
            },
            required: ['impressions', 'likes', 'comments', 'shares'],
        },
    },
    required: ['data'],
    definitions: {},
} as const satisfies JSONSchemaExtraProps;
