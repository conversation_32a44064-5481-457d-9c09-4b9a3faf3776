import { FromSchema } from 'json-schema-to-ts';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { regularPostInsightJSONSchema } from ':modules/post-insights/discriminators/regular-post/regular-post-insight-schema';
import { IPostInsightV2, PostInsightModelV2 } from ':modules/post-insights/post-insights-model-v2';

const regularPostInsightSchema = createMongooseSchemaFromJSONSchema(regularPostInsightJSONSchema);

export type IRegularPostInsight = IPostInsightV2 &
    FromSchema<
        typeof regularPostInsightJSONSchema,
        {
            keepDefaultedPropertiesOptional: true;
            deserialize: DESERIALIZE_OPTIONS;
        }
    >;

export const RegularPostInsightModel = PostInsightModelV2.discriminator<IRegularPostInsight>(
    regularPostInsightJSONSchema.title,
    regularPostInsightSchema
);
