import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const storyPostInsightJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'StoryPostInsight',
    type: 'object',
    discriminator: true,
    additionalProperties: false,
    properties: {
        data: {
            type: 'object',
            additionalProperties: false,
            properties: {
                impressions: {
                    type: 'number',
                    default: 0,
                },
                reach: {
                    type: 'number',
                    default: 0,
                },
                taps_forward: {
                    type: 'number',
                    default: 0,
                },
                taps_back: {
                    type: 'number',
                    default: 0,
                },
                replies: {
                    type: 'number',
                    default: 0,
                },
                taps_exits: {
                    type: 'number',
                    default: 0,
                },
            },
            required: ['impressions', 'reach', 'taps_forward', 'taps_back', 'replies', 'taps_exits'],
        },
    },
    required: ['data'],
    definitions: {},
} as const satisfies JSONSchemaExtraProps;
