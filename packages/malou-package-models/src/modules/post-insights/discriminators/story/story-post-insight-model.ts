import { FromSchema } from 'json-schema-to-ts';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { storyPostInsightJSONSchema } from ':modules/post-insights/discriminators/story/story-post-insight-schema';
import { IPostInsightV2, PostInsightModelV2 } from ':modules/post-insights/post-insights-model-v2';

const storyPostInsightSchema = createMongooseSchemaFromJSONSchema(storyPostInsightJSONSchema);

export type IStoryPostInsight = IPostInsightV2 &
    FromSchema<
        typeof storyPostInsightJSONSchema,
        {
            keepDefaultedPropertiesOptional: true;
            deserialize: DESERIALIZE_OPTIONS;
        }
    >;

export const StoryPostInsightModel = PostInsightModelV2.discriminator<IStoryPostInsight>(
    storyPostInsightJSONSchema.title,
    storyPostInsightSchema
);
