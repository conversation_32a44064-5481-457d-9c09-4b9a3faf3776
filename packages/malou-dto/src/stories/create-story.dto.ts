import z from 'zod';

import { DeviceType } from '@malou-io/package-utils';

import { objectIdValidator } from '../utils';

export const createStoryV2BodyValidator = z
    .object({
        restaurantId: objectIdValidator,
        date: z.string().datetime().optional(),
        createdFromDeviceType: z.nativeEnum(DeviceType),
    })
    .transform((data) => ({
        restaurantId: data.restaurantId,
        date: data.date ? new Date(data.date) : undefined,
        createdFromDeviceType: data.createdFromDeviceType,
    }));
export type CreateStoryV2BodyDto = z.infer<typeof createStoryV2BodyValidator>;
