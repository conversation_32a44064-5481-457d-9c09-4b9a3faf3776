import { z } from 'zod';

import { restaurantIdParamsTransformValidator } from '../common';

export interface GetPublishedStoriesCountDto {
    count: number;
}

export const getPublishedStoriesCountParamsValidator = restaurantIdParamsTransformValidator;
export type GetPublishedStoriesCountParamsDto = z.infer<typeof getPublishedStoriesCountParamsValidator>;

export const getPublishedStoriesCountQueryValidator = z
    .object({
        start_date: z.coerce.date(),
        end_date: z.coerce.date(),
    })
    .transform((data) => ({
        startDate: data.start_date,
        endDate: data.end_date,
    }));
export type GetPublishedStoriesCountQueryDto = z.infer<typeof getPublishedStoriesCountQueryValidator>;
