import { z } from 'zod';

import { DeviceType, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { postRefValidator } from '../posts/posts.request.dto';
import { objectIdValidator } from '../utils';
import { StoryDto } from './story.dto';

export const duplicateStoriesParamsValidator = z
    .object({
        restaurant_id: objectIdValidator,
    })
    .transform((data) => ({ restaurantId: data.restaurant_id }));

export type DuplicateStoriesParamsDto = z.infer<typeof duplicateStoriesParamsValidator>;

export const duplicateStoriesBodyValidator = z
    .object({
        postRefsToDuplicate: z.array(postRefValidator),
        restaurantIds: z.array(objectIdValidator),
        customFields: z
            .array(
                z.object({
                    restaurantId: z.string(),
                    plannedPublicationDate: z.string().datetime().optional(),
                    platformKeys: z.array(z.nativeEnum(PlatformKey)).optional(),
                    published: z.nativeEnum(PostPublicationStatus).optional(),
                })
            )
            .optional(),
        createdFromDeviceType: z.nativeEnum(DeviceType).nullish(),
    })
    .transform((data) => ({
        ...data,
        customFields: data.customFields
            ? data.customFields.map((customFields) => ({
                  ...customFields,
                  plannedPublicationDate: customFields.plannedPublicationDate ? new Date(customFields.plannedPublicationDate) : undefined,
              }))
            : undefined,
    }));

export type DuplicateStoriesBodyDto = z.infer<typeof duplicateStoriesBodyValidator>;

export type DuplicateStoriesResponseDto = {
    duplicatedStories: { story: StoryDto; restaurantId: string }[];
};
