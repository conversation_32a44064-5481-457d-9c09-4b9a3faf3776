import { z } from 'zod';

import { StoreLocatorLanguage } from '@malou-io/package-utils';

import { updateStoreLocatorParamsValidator } from '../shared';
import { storeLocatorMapComponentsValidator } from '../store-map';

// -------------------------------------------------------------------------------

export const updateStoreLocatorCentralizationPageParamsValidator = updateStoreLocatorParamsValidator;

export type UpdateStoreLocatorCentralizationPageParamsDto = z.infer<typeof updateStoreLocatorCentralizationPageParamsValidator>;

// -------------------------------------------------------------------------------
export const storeLocatorCentralizationPageCurrentUpdates = z.object({
    lang: z.nativeEnum(StoreLocatorLanguage),
    mapComponents: storeLocatorMapComponentsValidator.optional(),
});

export type StoreLocatorCentralizationPageUpdatesDto = z.infer<typeof storeLocatorCentralizationPageCurrentUpdates>;

// -------------------------------------------------------------------------------
export const updateStoreLocatorCentralizationPagesBodyValidator = z.array(storeLocatorCentralizationPageCurrentUpdates);

export type UpdateStoreLocatorCentralizationPagesBodyDto = z.infer<typeof updateStoreLocatorCentralizationPagesBodyValidator>;
